# Tab to Popup Converter - Title Saving & Always on Top Fixes

## Issues Fixed

### 1. Popup Title Saving Issue ✅
- **Problem**: Custom titles set in settings menu weren't being applied to popup windows
- **Root Cause**: Default title from settings wasn't being loaded during initialization
- **Solution**: Enhanced title loading and saving system

#### Implementation Details:
- Added `loadDefaultTitleFromSettings()` method to load title from Chrome storage
- Enhanced message listener to handle settings updates
- Improved background script to forward title save messages to all content scripts
- Added proper initialization of default title from settings

#### Code Changes:
- **scripts/tab-to-popup.js**: Added settings loading and message handling
- **background.js**: Enhanced title saving message forwarding
- **popup.js**: Existing settings interface already supported custom titles

### 2. Always on Top Setting ✅
- **Problem**: No option to keep popup windows on top of other windows
- **Solution**: Added "Always on Top" toggle in Settings tab with Chrome storage persistence

#### Implementation Details:
- Added toggle switch in Settings tab of extension popup
- Setting is saved to Chrome storage and persists across browser sessions
- Applies to all new popup windows created after enabling
- Uses workaround for Chrome extension limitations (periodic focusing)

#### Code Changes:
- **popup.html**: Added "Always on Top" toggle in Popup Window Settings section
- **styles/popup.css**: Added styling for toggle-label
- **popup.js**: Added setting handling (load/save/apply)
- **scripts/tab-to-popup.js**: Added alwaysOnTop to default config and message handling
- **background.js**: Added `setupAlwaysOnTop()` method with periodic focusing workaround

## Technical Implementation

### Title Saving Flow:
1. User sets "Default Popup Window Title" in Settings tab
2. Setting is saved to Chrome storage (`settings.popupDefaultTitle`)
3. Tab to Popup Converter loads this setting during initialization
4. When creating popup, uses saved title or falls back to "eLeadCRM Weblink"
5. User can still override with custom titles per URL using blue title bar

### Always on Top Flow:
1. User enables "Always on Top" toggle in Settings tab
2. Setting is saved to Chrome storage (`settings.popupAlwaysOnTop`)
3. Tab to Popup Converter includes this setting in popup creation config
4. Background script sets up periodic focusing (every 2 seconds) for "always on top" behavior
5. Interval is cleaned up when popup window is closed

## Chrome Extension Limitations

### Always on Top Workaround:
Chrome extensions don't support true `alwaysOnTop` for popup windows, so we implemented:
- Periodic checking (every 2 seconds) if popup window is focused
- If another window gains focus, automatically refocus the popup
- Smart logic to avoid interfering with user typing
- Automatic cleanup when popup is closed

## Settings Interface

### New Setting Added:
```html
<div class="setting-item">
    <label class="toggle-label">
        <span>Always on Top:</span>
        <label class="toggle">
            <input type="checkbox" id="popup-always-on-top">
            <span class="slider"></span>
        </label>
    </label>
    <div class="setting-description">Keep popup windows on top of other windows</div>
</div>
```

## Testing Instructions

### Test Title Saving:
1. Open extension popup → Settings tab
2. Set "Default Popup Window Title" to custom name (e.g., "My CRM Window")
3. Save settings
4. Navigate to eLeadCRM weblink iframe
5. Press Ctrl+Alt+9 to create popup
6. Verify popup title shows custom name, not "Weblead Today"

### Test Always on Top:
1. Open extension popup → Settings tab
2. Enable "Always on Top" toggle
3. Save settings
4. Create popup with Ctrl+Alt+9
5. Open another application window
6. Verify popup automatically comes back to front within 2 seconds

### Test Persistence:
1. Set both custom title and always on top
2. Close and reopen browser
3. Create new popup - settings should be preserved

## Expected Behavior
- ✅ Custom popup titles from settings are properly applied
- ✅ Always on top setting keeps popups focused
- ✅ Settings persist across browser sessions
- ✅ No interference with user typing or normal window management
- ✅ Automatic cleanup when popups are closed
