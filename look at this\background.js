import{i as re}from"./chunks/_virtual_wxt-plugins-CDnz5Vh6.js";var F=Object.prototype.hasOwnProperty;function U(a,e){var t,o;if(a===e)return!0;if(a&&e&&(t=a.constructor)===e.constructor){if(t===Date)return a.getTime()===e.getTime();if(t===RegExp)return a.toString()===e.toString();if(t===Array){if((o=a.length)===e.length)for(;o--&&U(a[o],e[o]););return o===-1}if(!t||typeof a=="object"){o=0;for(t in a)if(F.call(a,t)&&++o&&!F.call(e,t)||!(t in e)||!U(a[t],e[t]))return!1;return Object.keys(e).length===o}}return a!==a&&e!==e}const oe=new Error("request for lock canceled");var ae=function(a,e,t,o){function d(u){return u instanceof t?u:new t(function(h){h(u)})}return new(t||(t=Promise))(function(u,h){function m(v){try{b(o.next(v))}catch(I){h(I)}}function M(v){try{b(o.throw(v))}catch(I){h(I)}}function b(v){v.done?u(v.value):d(v.value).then(m,M)}b((o=o.apply(a,e||[])).next())})};class ie{constructor(e,t=oe){this._value=e,this._cancelError=t,this._queue=[],this._weightedWaiters=[]}acquire(e=1,t=0){if(e<=0)throw new Error(`invalid weight ${e}: must be positive`);return new Promise((o,d)=>{const u={resolve:o,reject:d,weight:e,priority:t},h=te(this._queue,m=>t<=m.priority);h===-1&&e<=this._value?this._dispatchItem(u):this._queue.splice(h+1,0,u)})}runExclusive(e){return ae(this,arguments,void 0,function*(t,o=1,d=0){const[u,h]=yield this.acquire(o,d);try{return yield t(u)}finally{h()}})}waitForUnlock(e=1,t=0){if(e<=0)throw new Error(`invalid weight ${e}: must be positive`);return this._couldLockImmediately(e,t)?Promise.resolve():new Promise(o=>{this._weightedWaiters[e-1]||(this._weightedWaiters[e-1]=[]),se(this._weightedWaiters[e-1],{resolve:o,priority:t})})}isLocked(){return this._value<=0}getValue(){return this._value}setValue(e){this._value=e,this._dispatchQueue()}release(e=1){if(e<=0)throw new Error(`invalid weight ${e}: must be positive`);this._value+=e,this._dispatchQueue()}cancel(){this._queue.forEach(e=>e.reject(this._cancelError)),this._queue=[]}_dispatchQueue(){for(this._drainUnlockWaiters();this._queue.length>0&&this._queue[0].weight<=this._value;)this._dispatchItem(this._queue.shift()),this._drainUnlockWaiters()}_dispatchItem(e){const t=this._value;this._value-=e.weight,e.resolve([t,this._newReleaser(e.weight)])}_newReleaser(e){let t=!1;return()=>{t||(t=!0,this.release(e))}}_drainUnlockWaiters(){if(this._queue.length===0)for(let e=this._value;e>0;e--){const t=this._weightedWaiters[e-1];t&&(t.forEach(o=>o.resolve()),this._weightedWaiters[e-1]=[])}else{const e=this._queue[0].priority;for(let t=this._value;t>0;t--){const o=this._weightedWaiters[t-1];if(!o)continue;const d=o.findIndex(u=>u.priority<=e);(d===-1?o:o.splice(0,d)).forEach(u=>u.resolve())}}}_couldLockImmediately(e,t){return(this._queue.length===0||this._queue[0].priority<t)&&e<=this._value}}function se(a,e){const t=te(a,o=>e.priority<=o.priority);a.splice(t+1,0,e)}function te(a,e){for(let t=a.length-1;t>=0;t--)if(e(a[t]))return t;return-1}var ce=function(a,e,t,o){function d(u){return u instanceof t?u:new t(function(h){h(u)})}return new(t||(t=Promise))(function(u,h){function m(v){try{b(o.next(v))}catch(I){h(I)}}function M(v){try{b(o.throw(v))}catch(I){h(I)}}function b(v){v.done?u(v.value):d(v.value).then(m,M)}b((o=o.apply(a,e||[])).next())})};class le{constructor(e){this._semaphore=new ie(1,e)}acquire(){return ce(this,arguments,void 0,function*(e=0){const[,t]=yield this._semaphore.acquire(1,e);return t})}runExclusive(e,t=0){return this._semaphore.runExclusive(()=>e(),1,t)}isLocked(){return this._semaphore.isLocked()}waitForUnlock(e=0){return this._semaphore.waitForUnlock(1,e)}release(){this._semaphore.isLocked()&&this._semaphore.release()}cancel(){return this._semaphore.cancel()}}var z,J;const $=((J=(z=globalThis.browser)==null?void 0:z.runtime)==null?void 0:J.id)==null?globalThis.chrome:globalThis.browser,q=ue();function ue(){const a={local:T("local"),session:T("session"),sync:T("sync"),managed:T("managed")},e=s=>{const n=a[s];if(n==null){const r=Object.keys(a).join(", ");throw Error(`Invalid area "${s}". Options: ${r}`)}return n},t=s=>{const n=s.indexOf(":"),r=s.substring(0,n),i=s.substring(n+1);if(i==null)throw Error(`Storage key should be in the form of "area:key", but received "${s}"`);return{driverArea:r,driverKey:i,driver:e(r)}},o=s=>s+"$",d=(s,n)=>{const r={...s};return Object.entries(n).forEach(([i,c])=>{c==null?delete r[i]:r[i]=c}),r},u=(s,n)=>s??n??null,h=s=>typeof s=="object"&&!Array.isArray(s)?s:{},m=async(s,n,r)=>{const i=await s.getItem(n);return u(i,(r==null?void 0:r.fallback)??(r==null?void 0:r.defaultValue))},M=async(s,n)=>{const r=o(n),i=await s.getItem(r);return h(i)},b=async(s,n,r)=>{await s.setItem(n,r??null)},v=async(s,n,r)=>{const i=o(n),c=h(await s.getItem(i));await s.setItem(i,d(c,r))},I=async(s,n,r)=>{if(await s.removeItem(n),r!=null&&r.removeMeta){const i=o(n);await s.removeItem(i)}},N=async(s,n,r)=>{const i=o(n);if(r==null)await s.removeItem(i);else{const c=h(await s.getItem(i));[r].flat().forEach(l=>delete c[l]),await s.setItem(i,c)}},j=(s,n,r)=>s.watch(n,r);return{getItem:async(s,n)=>{const{driver:r,driverKey:i}=t(s);return await m(r,i,n)},getItems:async s=>{const n=new Map,r=new Map,i=[];s.forEach(l=>{let f,g;typeof l=="string"?f=l:"getValue"in l?(f=l.key,g={fallback:l.fallback}):(f=l.key,g=l.options),i.push(f);const{driverArea:k,driverKey:y}=t(f),x=n.get(k)??[];n.set(k,x.concat(y)),r.set(f,g)});const c=new Map;return await Promise.all(Array.from(n.entries()).map(async([l,f])=>{(await a[l].getItems(f)).forEach(k=>{const y=`${l}:${k.key}`,x=r.get(y),w=u(k.value,(x==null?void 0:x.fallback)??(x==null?void 0:x.defaultValue));c.set(y,w)})})),i.map(l=>({key:l,value:c.get(l)}))},getMeta:async s=>{const{driver:n,driverKey:r}=t(s);return await M(n,r)},getMetas:async s=>{const n=s.map(c=>{const l=typeof c=="string"?c:c.key,{driverArea:f,driverKey:g}=t(l);return{key:l,driverArea:f,driverKey:g,driverMetaKey:o(g)}}),r=n.reduce((c,l)=>{var f;return c[f=l.driverArea]??(c[f]=[]),c[l.driverArea].push(l),c},{}),i={};return await Promise.all(Object.entries(r).map(async([c,l])=>{const f=await $.storage[c].get(l.map(g=>g.driverMetaKey));l.forEach(g=>{i[g.key]=f[g.driverMetaKey]??{}})})),n.map(c=>({key:c.key,meta:i[c.key]}))},setItem:async(s,n)=>{const{driver:r,driverKey:i}=t(s);await b(r,i,n)},setItems:async s=>{const n={};s.forEach(r=>{const{driverArea:i,driverKey:c}=t("key"in r?r.key:r.item.key);n[i]??(n[i]=[]),n[i].push({key:c,value:r.value})}),await Promise.all(Object.entries(n).map(async([r,i])=>{await e(r).setItems(i)}))},setMeta:async(s,n)=>{const{driver:r,driverKey:i}=t(s);await v(r,i,n)},setMetas:async s=>{const n={};s.forEach(r=>{const{driverArea:i,driverKey:c}=t("key"in r?r.key:r.item.key);n[i]??(n[i]=[]),n[i].push({key:c,properties:r.meta})}),await Promise.all(Object.entries(n).map(async([r,i])=>{const c=e(r),l=i.map(({key:y})=>o(y));console.log(r,l);const f=await c.getItems(l),g=Object.fromEntries(f.map(({key:y,value:x})=>[y,h(x)])),k=i.map(({key:y,properties:x})=>{const w=o(y);return{key:w,value:d(g[w]??{},x)}});await c.setItems(k)}))},removeItem:async(s,n)=>{const{driver:r,driverKey:i}=t(s);await I(r,i,n)},removeItems:async s=>{const n={};s.forEach(r=>{let i,c;typeof r=="string"?i=r:"getValue"in r?i=r.key:"item"in r?(i=r.item.key,c=r.options):(i=r.key,c=r.options);const{driverArea:l,driverKey:f}=t(i);n[l]??(n[l]=[]),n[l].push(f),c!=null&&c.removeMeta&&n[l].push(o(f))}),await Promise.all(Object.entries(n).map(async([r,i])=>{await e(r).removeItems(i)}))},clear:async s=>{await e(s).clear()},removeMeta:async(s,n)=>{const{driver:r,driverKey:i}=t(s);await N(r,i,n)},snapshot:async(s,n)=>{var c;const i=await e(s).snapshot();return(c=n==null?void 0:n.excludeKeys)==null||c.forEach(l=>{delete i[l],delete i[o(l)]}),i},restoreSnapshot:async(s,n)=>{await e(s).restoreSnapshot(n)},watch:(s,n)=>{const{driver:r,driverKey:i}=t(s);return j(r,i,n)},unwatch(){Object.values(a).forEach(s=>{s.unwatch()})},defineItem:(s,n)=>{const{driver:r,driverKey:i}=t(s),{version:c=1,migrations:l={}}=n??{};if(c<1)throw Error("Storage item version cannot be less than 1. Initial versions should be set to 1, not 0.");const f=async()=>{var A;const w=o(i),[{value:E},{value:_}]=await r.getItems([i,w]);if(E==null)return;const O=(_==null?void 0:_.v)??1;if(O>c)throw Error(`Version downgrade detected (v${O} -> v${c}) for "${s}"`);if(O===c)return;console.debug(`[@wxt-dev/storage] Running storage migration for ${s}: v${O} -> v${c}`);const ne=Array.from({length:c-O},(P,C)=>O+C+1);let V=E;for(const P of ne)try{V=await((A=l==null?void 0:l[P])==null?void 0:A.call(l,V))??V}catch(C){throw new de(s,P,{cause:C})}await r.setItems([{key:i,value:V},{key:w,value:{..._,v:c}}]),console.debug(`[@wxt-dev/storage] Storage migration completed for ${s} v${c}`,{migratedValue:V})},g=(n==null?void 0:n.migrations)==null?Promise.resolve():f().catch(w=>{console.error(`[@wxt-dev/storage] Migration failed for ${s}`,w)}),k=new le,y=()=>(n==null?void 0:n.fallback)??(n==null?void 0:n.defaultValue)??null,x=()=>k.runExclusive(async()=>{const w=await r.getItem(i);if(w!=null||(n==null?void 0:n.init)==null)return w;const E=await n.init();return await r.setItem(i,E),E});return g.then(x),{key:s,get defaultValue(){return y()},get fallback(){return y()},getValue:async()=>(await g,n!=null&&n.init?await x():await m(r,i,n)),getMeta:async()=>(await g,await M(r,i)),setValue:async w=>(await g,await b(r,i,w)),setMeta:async w=>(await g,await v(r,i,w)),removeValue:async w=>(await g,await I(r,i,w)),removeMeta:async w=>(await g,await N(r,i,w)),watch:w=>j(r,i,(E,_)=>w(E??y(),_??y())),migrate:f}}}}function T(a){const e=()=>{if($.runtime==null)throw Error(["'wxt/storage' must be loaded in a web extension environment",`
 - If thrown during a build, see https://github.com/wxt-dev/wxt/issues/371`,` - If thrown during tests, mock 'wxt/browser' correctly. See https://wxt.dev/guide/go-further/testing.html
`].join(`
`));if($.storage==null)throw Error("You must add the 'storage' permission to your manifest to use 'wxt/storage'");const o=$.storage[a];if(o==null)throw Error(`"browser.storage.${a}" is undefined`);return o},t=new Set;return{getItem:async o=>(await e().get(o))[o],getItems:async o=>{const d=await e().get(o);return o.map(u=>({key:u,value:d[u]??null}))},setItem:async(o,d)=>{d==null?await e().remove(o):await e().set({[o]:d})},setItems:async o=>{const d=o.reduce((u,{key:h,value:m})=>(u[h]=m,u),{});await e().set(d)},removeItem:async o=>{await e().remove(o)},removeItems:async o=>{await e().remove(o)},clear:async()=>{await e().clear()},snapshot:async()=>await e().get(),restoreSnapshot:async o=>{await e().set(o)},watch(o,d){const u=h=>{const m=h[o];m!=null&&(U(m.newValue,m.oldValue)||d(m.newValue??null,m.oldValue??null))};return e().onChanged.addListener(u),t.add(u),()=>{e().onChanged.removeListener(u),t.delete(u)}},unwatch(){t.forEach(o=>{e().onChanged.removeListener(o)}),t.clear()}}}class de extends Error{constructor(e,t,o){super(`v${t} migration failed for "${e}"`,o),this.key=e,this.version=t}}function he(a){return a==null||typeof a=="function"?{main:a}:a}var G,X;const H=(X=(G=globalThis.browser)==null?void 0:G.runtime)!=null&&X.id?globalThis.browser:globalThis.chrome;function me(){return{t:(e,...t)=>{let o,d;t.forEach((m,M)=>{if(m!=null)if(typeof m=="number")d=m;else if(Array.isArray(m))o=m;else throw Error(`Unknown argument at index ${M}. Must be a number for pluralization, substitution array, or options object.`)}),d!=null&&o==null&&(o=[String(d)]);let u;if(o!=null&&o.length){const m=o==null?void 0:o.map(M=>String(M));u=H.i18n.getMessage(e.replaceAll(".","_"),m)}else u=H.i18n.getMessage(e.replaceAll(".","_"));if(u||console.warn(`[i18n] Message not found: "${e}"`),d==null)return u;const h=u.split(" | ");switch(h.length){case 1:return h[0];case 2:return h[d===1?0:1];case 3:return h[d===0||d===1?d:2];default:throw Error("Unknown plural formatting")}}}}const p=me(),D=q.defineItem("local:extCurrentVersion"),Q=q.defineItem("local:extUpdateShown",{defaultValue:!1}),fe=q.defineItem("local:extLastUrl"),K=q.defineItem("local:extFloatingButtonEnabled",{defaultValue:!0}),W=q.defineItem("local:extLinkContextMenuEnabled",{defaultValue:!0});async function ge(){const a=chrome.runtime.getManifest().version,e=await D.getValue();a!==e&&(await D.setValue(a),await Q.getValue()||await Q.setValue(!0))}async function we(a){try{await chrome.declarativeNetRequest.updateSessionRules({addRules:[{id:1,priority:1,action:{type:chrome.declarativeNetRequest.RuleActionType.MODIFY_HEADERS,responseHeaders:[{header:"x-frame-options",operation:chrome.declarativeNetRequest.HeaderOperation.REMOVE},{header:"frame-options",operation:chrome.declarativeNetRequest.HeaderOperation.REMOVE},{header:"content-security-policy",operation:chrome.declarativeNetRequest.HeaderOperation.REMOVE},{header:"content-security-policy-report-only",operation:chrome.declarativeNetRequest.HeaderOperation.REMOVE},{header:"allow-from",operation:chrome.declarativeNetRequest.HeaderOperation.SET,value:"*"},{header:"frame-ancestors",operation:chrome.declarativeNetRequest.HeaderOperation.SET,value:"*"}],requestHeaders:[{header:"Sec-Fetch-Site",operation:chrome.declarativeNetRequest.HeaderOperation.SET,value:"same-origin"},{header:"If-None-Match",operation:chrome.declarativeNetRequest.HeaderOperation.REMOVE}]},condition:{urlFilter:"*"}}]})}catch(e){console.error("Error adding network rules:",e)}}async function B(){try{const e=(await chrome.declarativeNetRequest.getSessionRules()).map(t=>t.id);await chrome.declarativeNetRequest.updateSessionRules({addRules:[],removeRuleIds:e})}catch(a){console.error("Error removing rules:",a)}}async function R(a,e){await B(),await we()}async function S(a){try{await fe.setValue(a)}catch(e){console.error("Error storing URL:",e)}}function pe(){chrome.contextMenus.create({id:"openInCompanionWindow",title:p.t("openInCompanionWindow"),contexts:["page"]}),chrome.contextMenus.create({id:"openLinkInCompanionWindow",title:p.t("openLinkInCompanionWindow"),contexts:["link"]}),chrome.contextMenus.create({id:"options",title:p.t("options"),contexts:["action"]}),chrome.contextMenus.create({id:"contextMenuToggle",title:p.t("contextMenu"),parentId:"options",contexts:["action"]}),chrome.contextMenus.create({id:"pageToggle",title:p.t("inPage"),parentId:"contextMenuToggle",contexts:["action"]}),chrome.contextMenus.create({id:"pageOn",title:p.t("on"),type:"radio",checked:!0,parentId:"pageToggle",contexts:["action"]}),chrome.contextMenus.create({id:"pageOff",title:p.t("off"),type:"radio",checked:!1,parentId:"pageToggle",contexts:["action"]}),chrome.contextMenus.create({id:"linkToggle",title:p.t("onLink"),parentId:"contextMenuToggle",contexts:["action"]}),chrome.contextMenus.create({id:"linkOn",title:p.t("on"),type:"radio",checked:!0,parentId:"linkToggle",contexts:["action"]}),chrome.contextMenus.create({id:"linkOff",title:p.t("off"),type:"radio",checked:!1,parentId:"linkToggle",contexts:["action"]}),chrome.contextMenus.create({id:"floatingButtonToggle",title:p.t("floatingButton"),parentId:"options",contexts:["action"]}),chrome.contextMenus.create({id:"floatingButtonOn",title:p.t("on"),type:"radio",checked:!0,parentId:"floatingButtonToggle",contexts:["action"]}),chrome.contextMenus.create({id:"floatingButtonOff",title:p.t("off"),type:"radio",checked:!1,parentId:"floatingButtonToggle",contexts:["action"]}),chrome.contextMenus.create({id:"keyboardShortcuts",title:p.t("keyboardShortcuts"),parentId:"options",contexts:["action"]}),chrome.contextMenus.create({id:"separator1",type:"separator",contexts:["action"]}),chrome.contextMenus.create({id:"support",title:p.t("support"),contexts:["action"]}),chrome.contextMenus.create({id:"issues",title:p.t("issuesAndSuggestions"),contexts:["action"]}),chrome.contextMenus.create({id:"github",title:p.t("github"),parentId:"issues",contexts:["action"]}),chrome.contextMenus.create({id:"reportIssue",title:p.t("reportIssue"),parentId:"issues",contexts:["action"]}),chrome.contextMenus.create({id:"donate",title:p.t("buyMeACoffee"),parentId:"support",contexts:["action"]}),chrome.contextMenus.create({id:"review",title:p.t("leaveAReview"),parentId:"support",contexts:["action"]})}const ve=he({type:"module",main(){chrome.runtime.onInstalled.addListener(async a=>{await ge(),chrome.contextMenus.removeAll(()=>{pe()}),await W.setValue(!0),await K.setValue(!0),a.reason==="install"&&chrome.tabs.create({url:chrome.runtime.getURL("/welcome.html"),active:!0})}),chrome.runtime.onMessage.addListener((a,e,t)=>{a.action==="cleanupRules"&&(B(),t(!0))}),chrome.runtime.onSuspend.addListener(()=>{B()}),chrome.action.onClicked.addListener(a=>{if(!a.url||!a.id)return;const e=d=>{const u=encodeURIComponent(`<!DOCTYPE html><html><body><script>alert(${JSON.stringify(d)});window.close();<\/script></body></html>`);chrome.tabs.create({url:`data:text/html,${u}`,active:!0})},t=["chrome.google.com","chromewebstore.google.com","edge.microsoft.com","microsoftedge.microsoft.com"],o=["chrome:","chrome-extension:","edge:","edge-extension:","extension:"];try{const d=new URL(a.url);if(o.includes(d.protocol)||t.includes(d.hostname)){e(`${p.t("unsupportedHost")} (${d.hostname})`);return}}catch{}R(a.url),chrome.tabs.sendMessage(a.id,{action:"openPiP"},()=>{chrome.runtime.lastError&&(console.warn("No receiver for openPiP message, likely fresh install / blocked page"),e(p.t("reloadTabOrBrowser")))}),S(a.url)}),chrome.contextMenus.onClicked.addListener(async(a,e)=>{if(e!=null&&e.id)switch(a.menuItemId){case"openInCompanionWindow":e.url&&(R(e.url),chrome.tabs.sendMessage(e.id,{action:"openPiP"}),S(e.url));break;case"pageOn":chrome.contextMenus.update("openInCompanionWindow",{visible:!0}),chrome.contextMenus.update("pageOn",{checked:!0}),chrome.contextMenus.update("pageOff",{checked:!1});break;case"pageOff":chrome.contextMenus.update("openInCompanionWindow",{visible:!1}),chrome.contextMenus.update("pageOn",{checked:!1}),chrome.contextMenus.update("pageOff",{checked:!0});break;case"openLinkInCompanionWindow":a.linkUrl?(R(a.linkUrl),chrome.tabs.sendMessage(e.id,{action:"openPiP"}),S(a.linkUrl)):console.error("No link URL found in context menu info");break;case"linkOn":await W.setValue(!0),chrome.contextMenus.update("openLinkInCompanionWindow",{visible:!0}),chrome.contextMenus.update("linkOn",{checked:!0}),chrome.contextMenus.update("linkOff",{checked:!1});break;case"linkOff":await W.setValue(!1),chrome.contextMenus.update("openLinkInCompanionWindow",{visible:!1}),chrome.contextMenus.update("linkOn",{checked:!1}),chrome.contextMenus.update("linkOff",{checked:!0});break;case"floatingButtonOn":await K.setValue(!0),chrome.contextMenus.update("floatingButtonOn",{checked:!0}),chrome.contextMenus.update("floatingButtonOff",{checked:!1}),chrome.tabs.query({},t=>{t.forEach(o=>{o.id&&chrome.tabs.sendMessage(o.id,{action:"toggleFloatingButton",state:!0})})});break;case"floatingButtonOff":await K.setValue(!1),chrome.contextMenus.update("floatingButtonOn",{checked:!1}),chrome.contextMenus.update("floatingButtonOff",{checked:!0}),chrome.tabs.query({},t=>{t.forEach(o=>{o.id&&chrome.tabs.sendMessage(o.id,{action:"toggleFloatingButton",state:!1})})});break;case"keyboardShortcuts":chrome.tabs.create({url:"chrome://extensions/shortcuts#:~:text=Toggle%20Companion%20Window"});break;case"github":chrome.tabs.create({url:"https://github.com/Mohamed3nan/CompanionWindow"});break;case"reportIssue":chrome.tabs.create({url:"https://github.com/Mohamed3nan/CompanionWindow/issues"});break;case"donate":chrome.tabs.create({url:"https://ko-fi.com/mohamed3nan"});break;case"review":chrome.tabs.create({url:`https://chromewebstore.google.com/detail/${chrome.runtime.id}/reviews`});break}}),chrome.commands.onCommand.addListener(async a=>{if(a==="toggle-companion-window"){const[e]=await chrome.tabs.query({active:!0,currentWindow:!0});if(e!=null&&e.id&&e.url)try{const t=await chrome.tabs.sendMessage(e.id,{action:"checkPiPWindow"});t&&t.hasPiPWindow?chrome.tabs.sendMessage(e.id,{action:"closePiP"}):(R(e.url,!0),chrome.tabs.sendMessage(e.id,{action:"openPiP"}),S(e.url))}catch{R(e.url),chrome.tabs.sendMessage(e.id,{action:"openPiP"}),S(e.url)}}})}});var Z,ee;(ee=(Z=globalThis.browser)==null?void 0:Z.runtime)!=null&&ee.id?globalThis.browser:globalThis.chrome;function L(a,...e){}const ye={debug:(...a)=>L(console.debug,...a),log:(...a)=>L(console.log,...a),warn:(...a)=>L(console.warn,...a),error:(...a)=>L(console.error,...a)};let Y;try{re(),Y=ve.main(),Y instanceof Promise&&console.warn("The background's main() function return a promise, but it must be synchronous")}catch(a){throw ye.error("The background crashed on startup!"),a}
