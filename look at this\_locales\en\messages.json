{"buyMeACoffee": {"message": "☕ Buy me a coffee"}, "contextMenu": {"message": "🖱️ Context Menu"}, "extensionDescription": {"message": "Transform any webpage into a floating, always-on-top window for seamless multitasking and enhanced productivity"}, "extensionName": {"message": "Companion Window | Always on Top"}, "floatingButton": {"message": "••• Floating Button"}, "github": {"message": "🌐 GitHub"}, "inPage": {"message": "In Page"}, "issuesAndSuggestions": {"message": "🤔 Issues and Suggestions"}, "keyboardShortcuts": {"message": "⌨️ Keyboard Shortcuts"}, "leaveAReview": {"message": "🌟 Leave a review"}, "off": {"message": "Off"}, "on": {"message": "On"}, "onLink": {"message": "On Link"}, "openInCompanionWindow": {"message": "Open in Companion Window"}, "openLinkInCompanionWindow": {"message": "Open link in Companion Window"}, "options": {"message": "⚙️ Options"}, "reloadTabOrBrowser": {"message": "Seems like Companion Window was just installed or updated. Please reload the tab or restart your browser."}, "reportIssue": {"message": "🐛 Report Issue"}, "support": {"message": "❤️ Support"}, "toggleCompanionWindow": {"message": "Toggle Companion Window"}, "unsupportedHost": {"message": "Companion Window: not supported on this host"}}