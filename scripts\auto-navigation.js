// TrueBDC CRM Automation Suite - Auto Navigation
// Automatically navigates to specified link after page refresh

class AutoNavigation {
    constructor(settings = {}) {
        this.settings = settings;
        this.isActive = false;
        this.targetLinkText = '';
        this.navigationDelay = 1000; // 1 second delay
        
        this.init();
    }

    async init() {
        try {
            // Check if we're on a supported page
            if (this.isSupportedPage()) {
                await this.loadSettings();
                
                // Only activate if enabled in settings
                if (this.settings.autoNavigationEnabled) {
                    this.setupAutoNavigation();
                    this.isActive = true;
                }
                
                TrueBDCUtils.log('Auto Navigation initialized', {
                    isActive: this.isActive,
                    targetLinkText: this.targetLinkText,
                    delay: this.navigationDelay
                });
            }
        } catch (error) {
            TrueBDCUtils.error('Error initializing Auto Navigation', error);
        }
    }

    async loadSettings() {
        try {
            const result = await chrome.storage.local.get('settings');
            if (result.settings) {
                this.settings = result.settings;
                this.targetLinkText = result.settings.autoNavigationTarget || 'Internet Sales Rep';
                this.navigationDelay = result.settings.autoNavigationDelay || 1000;
                
                TrueBDCUtils.log('Auto Navigation settings loaded', {
                    enabled: this.settings.autoNavigationEnabled,
                    target: this.targetLinkText,
                    delay: this.navigationDelay
                });
            }
        } catch (error) {
            TrueBDCUtils.error('Error loading Auto Navigation settings', error);
        }
    }

    isSupportedPage() {
        // Support eLeadCRM and VinSolutions
        const url = window.location.href;
        return /eleadcrm\.com|vinsolutions\.com/i.test(url);
    }

    setupAutoNavigation() {
        // Only run on full page loads, not AJAX navigation
        if (document.readyState === 'loading') {
            // Page is still loading
            window.addEventListener('load', () => {
                this.performAutoNavigation();
            });
        } else if (document.readyState === 'complete') {
            // Page already loaded
            this.performAutoNavigation();
        }
    }

    performAutoNavigation() {
        // Wait for specified delay to ensure all elements are loaded
        setTimeout(() => {
            this.findAndClickTarget();
        }, this.navigationDelay);
    }

    findAndClickTarget() {
        try {
            // Get all anchor elements within the page
            const anchors = document.querySelectorAll('a');
            
            TrueBDCUtils.log('Auto Navigation searching for target', {
                targetText: this.targetLinkText,
                totalAnchors: anchors.length
            });

            // Find the anchor with the specified text
            for (let i = 0; i < anchors.length; i++) {
                const anchorText = anchors[i].textContent.trim();
                
                if (anchorText === this.targetLinkText) {
                    // Found the target link
                    TrueBDCUtils.log('Auto Navigation target found, clicking', {
                        targetText: this.targetLinkText,
                        href: anchors[i].href,
                        element: anchors[i]
                    });

                    // Simulate a click on the found anchor
                    anchors[i].click();
                    
                    // Log activity
                    TrueBDCUtils.logActivity('auto_navigation_clicked', {
                        targetText: this.targetLinkText,
                        href: anchors[i].href,
                        timestamp: new Date().toISOString()
                    });

                    // Show success notification
                    this.showNavigationNotification(this.targetLinkText, true);
                    
                    break;
                }
            }

            // If we get here and haven't found the target, log it
            const foundTarget = Array.from(anchors).some(anchor => 
                anchor.textContent.trim() === this.targetLinkText
            );
            
            if (!foundTarget) {
                TrueBDCUtils.log('Auto Navigation target not found', {
                    targetText: this.targetLinkText,
                    availableLinks: Array.from(anchors).map(a => a.textContent.trim()).filter(text => text.length > 0)
                });

                // Show not found notification
                this.showNavigationNotification(this.targetLinkText, false);
            }
        } catch (error) {
            TrueBDCUtils.error('Error in Auto Navigation findAndClickTarget', error);
        }
    }

    showNavigationNotification(targetText, success) {
        const notification = TrueBDCUtils.createElement('div', {
            id: 'truebdc-auto-navigation-notification'
        }, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: success ? 'linear-gradient(135deg, #28a745, #1e7e34)' : 'linear-gradient(135deg, #ffc107, #e0a800)',
            color: 'white',
            padding: '12px 16px',
            borderRadius: '6px',
            fontSize: '13px',
            fontWeight: '500',
            zIndex: '999999',
            boxShadow: success ? '0 3px 10px rgba(40, 167, 69, 0.3)' : '0 3px 10px rgba(255, 193, 7, 0.3)',
            opacity: '0',
            transition: 'opacity 0.3s ease'
        });

        const icon = success ? '🎯' : '⚠️';
        const message = success ? 
            `Auto-navigated to "${targetText}"` : 
            `Target "${targetText}" not found`;

        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 8px;">
                <span>${icon}</span>
                <span>${message}</span>
            </div>
        `;

        document.body.appendChild(notification);

        // Fade in
        setTimeout(() => {
            notification.style.opacity = '1';
        }, 10);

        // Fade out and remove
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, success ? 2000 : 4000); // Show longer if not found
    }

    async updateSettings(newSettings) {
        if (newSettings.autoNavigationEnabled !== undefined) {
            const wasActive = this.isActive;
            this.settings = newSettings;
            this.targetLinkText = newSettings.autoNavigationTarget || 'Internet Sales Rep';
            this.navigationDelay = newSettings.autoNavigationDelay || 1000;
            
            if (newSettings.autoNavigationEnabled && !wasActive) {
                // Just enabled - set up for next page load
                this.isActive = true;
                TrueBDCUtils.log('Auto Navigation enabled');
            } else if (!newSettings.autoNavigationEnabled && wasActive) {
                // Just disabled
                this.isActive = false;
                TrueBDCUtils.log('Auto Navigation disabled');
            }
        }
    }

    destroy() {
        this.isActive = false;
        TrueBDCUtils.log('Auto Navigation destroyed');
    }

    // Static method to check if script should be available
    static isAvailable() {
        const url = window.location.href;
        return /eleadcrm\.com|vinsolutions\.com/i.test(url);
    }
}

// Auto-initialize if available
if (typeof window !== 'undefined' && AutoNavigation.isAvailable()) {
    window.trueBDCAutoNavigation = new AutoNavigation();
}
