// TrueBDC CRM Automation Suite - Auto Navigation
// Automatically navigates to "Internet Sales Rep" link after page refresh
// Simplified version - no settings complexity, just script toggle

class AutoNavigation {
    constructor(settings = {}) {
        this.settings = settings;
        this.isActive = false;
        this.targetLinkText = 'Internet Sales Rep'; // Fixed target
        this.navigationDelay = 1000; // Fixed 1 second delay

        console.log('[AutoNavigation] Constructor called');
        console.log('[AutoNavigation] Current URL:', window.location.href);
        console.log('[AutoNavigation] Document ready state:', document.readyState);

        this.init();
    }

    async init() {
        try {
            console.log('[AutoNavigation] Init started');

            // Check if we're on a supported page
            if (this.isSupportedPage()) {
                console.log('[AutoNavigation] Page is supported, setting up auto navigation');
                this.setupAutoNavigation();
                this.isActive = true;

                console.log('[AutoNavigation] Auto Navigation activated');
                TrueBDCUtils.log('Auto Navigation initialized', {
                    isActive: this.isActive,
                    targetLinkText: this.targetLinkText,
                    delay: this.navigationDelay,
                    url: window.location.href
                });
            } else {
                console.log('[AutoNavigation] Page not supported, skipping');
                console.log('[AutoNavigation] Current URL:', window.location.href);
                console.log('[AutoNavigation] Required URL pattern: eleadcrm.com/evo2/fresh/elead-v45/elead_track/index.aspx');
            }
        } catch (error) {
            console.error('[AutoNavigation] Error in init:', error);
            TrueBDCUtils.error('Error initializing Auto Navigation', error);
        }
    }

    isSupportedPage() {
        // Only run on the specific eLeadCRM index page where "Internet Sales Rep" tab exists
        const url = window.location.href;
        const isSupported = url.includes('eleadcrm.com/evo2/fresh/elead-v45/elead_track/index.aspx');

        console.log('[AutoNavigation] URL check:', {
            currentUrl: url,
            isSupported: isSupported,
            requiredPattern: 'eleadcrm.com/evo2/fresh/elead-v45/elead_track/index.aspx'
        });

        return isSupported;
    }

    setupAutoNavigation() {
        console.log('[AutoNavigation] Setting up auto navigation');
        console.log('[AutoNavigation] Document ready state:', document.readyState);

        // EXACT COPY of Tampermonkey approach
        window.addEventListener('load', () => {
            console.log('[AutoNavigation] Window load event fired');
            this.performAutoNavigation();
        });

        // Also try immediately if page is already loaded
        if (document.readyState === 'complete') {
            console.log('[AutoNavigation] Page already complete, performing navigation immediately');
            this.performAutoNavigation();
        }
    }

    performAutoNavigation() {
        console.log('[AutoNavigation] Performing auto navigation with delay:', this.navigationDelay);

        // Wait for specified delay to ensure all elements are loaded (EXACT COPY of Tampermonkey)
        setTimeout(() => {
            console.log('[AutoNavigation] Delay completed, finding and clicking target');
            this.findAndClickTarget();
        }, this.navigationDelay);
    }

    findAndClickTarget() {
        try {
            console.log('[AutoNavigation] Starting findAndClickTarget');

            // EXACT COPY of Tampermonkey logic
            // Get all anchor elements within the dashboard
            const anchors = document.querySelectorAll('a');

            console.log('[AutoNavigation] Found anchors:', anchors.length);
            console.log('[AutoNavigation] Looking for target text:', this.targetLinkText);

            // Log all anchor texts for debugging
            const anchorTexts = Array.from(anchors).map((a, index) => ({
                index: index,
                text: a.textContent.trim(),
                href: a.href,
                visible: a.offsetParent !== null
            }));
            console.log('[AutoNavigation] All anchor texts:', anchorTexts);

            TrueBDCUtils.log('Auto Navigation searching for target', {
                targetText: this.targetLinkText,
                totalAnchors: anchors.length,
                allTexts: anchorTexts.map(a => a.text).filter(text => text.length > 0)
            });

            // Find the anchor with the text "Internet Sales Rep" (EXACT COPY)
            for (let i = 0; i < anchors.length; i++) {
                const anchorText = anchors[i].textContent.trim();

                console.log(`[AutoNavigation] Checking anchor ${i}: "${anchorText}"`);

                if (anchorText === this.targetLinkText) {
                    console.log('[AutoNavigation] TARGET FOUND! Clicking anchor:', anchors[i]);

                    // Found the target link
                    TrueBDCUtils.log('Auto Navigation target found, clicking', {
                        targetText: this.targetLinkText,
                        href: anchors[i].href,
                        element: anchors[i]
                    });

                    // Simulate a click on the found anchor (EXACT COPY)
                    anchors[i].click();

                    console.log('[AutoNavigation] Click executed successfully');

                    // Log activity
                    TrueBDCUtils.logActivity('auto_navigation_clicked', {
                        targetText: this.targetLinkText,
                        href: anchors[i].href,
                        timestamp: new Date().toISOString()
                    });

                    // Show success notification
                    this.showNavigationNotification(this.targetLinkText, true);

                    return; // Exit after successful click
                }
            }

            // If we get here, target was not found
            console.log('[AutoNavigation] TARGET NOT FOUND');
            console.log('[AutoNavigation] Available link texts:', anchorTexts.map(a => a.text).filter(text => text.length > 0));

            TrueBDCUtils.log('Auto Navigation target not found', {
                targetText: this.targetLinkText,
                availableLinks: anchorTexts.map(a => a.text).filter(text => text.length > 0)
            });

            // Show not found notification
            this.showNavigationNotification(this.targetLinkText, false);

        } catch (error) {
            console.error('[AutoNavigation] Error in findAndClickTarget:', error);
            TrueBDCUtils.error('Error in Auto Navigation findAndClickTarget', error);
        }
    }

    showNavigationNotification(targetText, success) {
        const notification = TrueBDCUtils.createElement('div', {
            id: 'truebdc-auto-navigation-notification'
        }, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: success ? 'linear-gradient(135deg, #28a745, #1e7e34)' : 'linear-gradient(135deg, #ffc107, #e0a800)',
            color: 'white',
            padding: '12px 16px',
            borderRadius: '6px',
            fontSize: '13px',
            fontWeight: '500',
            zIndex: '999999',
            boxShadow: success ? '0 3px 10px rgba(40, 167, 69, 0.3)' : '0 3px 10px rgba(255, 193, 7, 0.3)',
            opacity: '0',
            transition: 'opacity 0.3s ease'
        });

        const icon = success ? '🎯' : '⚠️';
        const message = success ? 
            `Auto-navigated to "${targetText}"` : 
            `Target "${targetText}" not found`;

        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 8px;">
                <span>${icon}</span>
                <span>${message}</span>
            </div>
        `;

        document.body.appendChild(notification);

        // Fade in
        setTimeout(() => {
            notification.style.opacity = '1';
        }, 10);

        // Fade out and remove
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, success ? 2000 : 4000); // Show longer if not found
    }

    // Settings management removed - script is controlled by toggle only

    destroy() {
        this.isActive = false;
        console.log('[AutoNavigation] Auto Navigation destroyed');
        TrueBDCUtils.log('Auto Navigation destroyed');
    }

    // Static method to check if script should be available
    static isAvailable() {
        const url = window.location.href;
        return url.includes('eleadcrm.com/evo2/fresh/elead-v45/elead_track/index.aspx');
    }
}

// Script loaded - will be initialized by content script when enabled
console.log('[AutoNavigation] Script class loaded, waiting for content script control');
