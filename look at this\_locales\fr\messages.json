{"buyMeACoffee": {"message": "☕ M'offrir un café"}, "contextMenu": {"message": "🖱️ Menu Contextuel"}, "extensionDescription": {"message": "Transformez n'importe quelle page web en une fenêtre flottante toujours visible pour un multitâche fluide et une productivité accrue"}, "extensionName": {"message": "Fenêtre Compagnon | Toujours Visible"}, "floatingButton": {"message": "••• Bouton Flottant"}, "github": {"message": "🌐 GitHub"}, "inPage": {"message": "<PERSON><PERSON>"}, "issuesAndSuggestions": {"message": "🤔 Problèmes et Suggestions"}, "keyboardShortcuts": {"message": "⌨️ <PERSON><PERSON><PERSON><PERSON>"}, "leaveAReview": {"message": "🌟 Laisser un avis"}, "off": {"message": "Désactivé"}, "on": {"message": "Activé"}, "onLink": {"message": "Sur le Lien"}, "openInCompanionWindow": {"message": "Ouv<PERSON>r dans la Fenêtre Compagnon"}, "openLinkInCompanionWindow": {"message": "<PERSON><PERSON><PERSON><PERSON>r le lien dans la Fenêtre Compagnon"}, "options": {"message": "⚙️ Options"}, "reloadTabOrBrowser": {"message": "Il semble que Companion Window vient d'être installé ou mis à jour. Veuillez recharger l'onglet ou redémarrer votre navigateur."}, "reportIssue": {"message": "🐛 Signaler un Problème"}, "support": {"message": "❤️ Support"}, "toggleCompanionWindow": {"message": "Basculer la Fenêtre Compagnon"}, "unsupportedHost": {"message": "Companion Window : non pris en charge sur cet hôte"}}