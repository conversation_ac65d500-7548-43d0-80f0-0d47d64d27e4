// TrueBDC CRM Automation Suite - Tab to Popup Converter

class TabToPopup {
    constructor(settings = {}) {
        this.settings = settings;
        this.isActive = false;
        this.keydownHandler = null;
        this.popup = null;

        // Enhanced default configuration with position
        this.defaultConfig = {
            width: 1200,
            height: 800,
            left: Math.round((screen.width - 1200) / 2),
            top: Math.round((screen.height - 800) / 2),
            alwaysOnTop: false
        };

        // Enhanced storage for both size and position
        this.savedConfigs = new Map();
        this.currentUrlKey = null;
        this.saveTimeout = null;
        this.isResizing = false;
        this.resizeEndTimeout = null;

        // Set up message listener for title saving
        this.setupMessageListener();

        this.init();
    }

    setupMessageListener() {
        // Listen for messages from settings
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            if (message.action === 'updateDefaultPopupConfig') {
                // Update default configuration from settings
                this.defaultConfig = message.config;
                TrueBDCUtils.log('Default popup config updated', {
                    config: message.config
                });
                sendResponse({ success: true });
            } else if (message.action === 'updateSettings') {
                // Update settings including always on top
                if (message.settings && message.settings.popupAlwaysOnTop !== undefined) {
                    this.defaultConfig.alwaysOnTop = message.settings.popupAlwaysOnTop;
                    TrueBDCUtils.log('Popup always on top setting updated', {
                        alwaysOnTop: this.defaultConfig.alwaysOnTop
                    });
                }
                sendResponse({ success: true });
            }
        });
    }

    init() {
        try {
            const frameContext = TrueBDCUtils.getFrameContext();
            TrueBDCUtils.log('Initializing Tab to Popup Converter', {
                frameContext: frameContext
            });

            // Check if we're on a supported page
            if (this.isSupportedPage()) {
                this.loadSavedConfigs();
                this.loadAlwaysOnTopSetting();
                this.setupKeyListener();
                this.isActive = true;

                TrueBDCUtils.log('Tab to Popup Converter activated', {
                    frameContext: frameContext
                });
                TrueBDCUtils.logActivity('tab_to_popup_activated', {
                    url: window.location.href,
                    frameContext: frameContext
                });
            } else {
                TrueBDCUtils.log('Tab to Popup Converter not activated - unsupported page', {
                    url: window.location.href,
                    frameContext: frameContext
                });
            }
        } catch (error) {
            TrueBDCUtils.error('Failed to initialize Tab to Popup Converter', error);
        }
    }

    async loadAlwaysOnTopSetting() {
        try {
            const result = await chrome.storage.local.get('settings');
            if (result.settings && result.settings.popupAlwaysOnTop !== undefined) {
                this.defaultConfig.alwaysOnTop = result.settings.popupAlwaysOnTop;
                TrueBDCUtils.log('Loaded always on top setting from storage', {
                    alwaysOnTop: this.defaultConfig.alwaysOnTop
                });
            }
        } catch (error) {
            TrueBDCUtils.error('Error loading always on top setting from storage', error);
        }
    }

    isSupportedPage() {
        const url = window.location.href;
        const frameContext = TrueBDCUtils.getFrameContext();

        // Use EXACT same URL matching as auto-refresh timer
        // Only support the same iframe where auto-refresh timer works
        const isSupported = url.includes('eleadcrm.com') &&
                           url.includes('/elead_track/weblink/weblinkToday.aspx');

        TrueBDCUtils.log('Tab to Popup page support check', {
            url: url,
            frameContext: frameContext,
            isSupported: isSupported,
            lookingFor: '/elead_track/weblink/weblinkToday.aspx'
        });

        return isSupported;
    }

    async loadSavedConfigs() {
        try {
            // Load popup configurations
            const result = await chrome.storage.local.get('popupConfigs');

            if (result.popupConfigs) {
                this.savedConfigs = new Map(Object.entries(result.popupConfigs));
                TrueBDCUtils.log('Loaded saved popup configurations', Object.fromEntries(this.savedConfigs));
            } else {
                // Migrate old popupSizes data if it exists
                const oldResult = await chrome.storage.local.get('popupSizes');
                if (oldResult.popupSizes) {
                    const oldSizes = Object.entries(oldResult.popupSizes);
                    for (const [key, size] of oldSizes) {
                        this.savedConfigs.set(key, {
                            width: size.width,
                            height: size.height,
                            left: Math.round((screen.width - size.width) / 2),
                            top: Math.round((screen.height - size.height) / 2)
                        });
                    }
                    await this.saveConfigs();
                    TrueBDCUtils.log('Migrated old popup sizes to new configuration format');
                }
            }


        } catch (error) {
            TrueBDCUtils.error('Error loading saved popup configurations', error);
        }
    }

    async saveConfigs() {
        try {
            const configsObject = Object.fromEntries(this.savedConfigs);
            await chrome.storage.local.set({ popupConfigs: configsObject });
            TrueBDCUtils.log('Saved popup configurations', configsObject);

            // Show visual feedback
            this.showConfigSavedNotification();
        } catch (error) {
            TrueBDCUtils.error('Error saving popup configurations', error);
        }
    }



    showConfigSavedNotification() {
        const notification = TrueBDCUtils.createElement('div', {
            id: 'truebdc-config-saved-notification'
        }, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: 'linear-gradient(135deg, #28a745, #20c997)',
            color: 'white',
            padding: '12px 16px',
            borderRadius: '6px',
            fontSize: '13px',
            fontWeight: '500',
            zIndex: '999999',
            boxShadow: '0 3px 10px rgba(40, 167, 69, 0.3)',
            opacity: '0',
            transition: 'opacity 0.3s ease'
        });

        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 8px;">
                <span>✓</span>
                <span>Popup position & size saved</span>
            </div>
        `;

        document.body.appendChild(notification);

        // Fade in
        setTimeout(() => {
            notification.style.opacity = '1';
        }, 10);

        // Fade out and remove
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 2000);
    }

    showTitleSavedNotification() {
        const notification = TrueBDCUtils.createElement('div', {
            id: 'truebdc-title-saved-notification'
        }, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: 'linear-gradient(135deg, #17a2b8, #138496)',
            color: 'white',
            padding: '12px 16px',
            borderRadius: '6px',
            fontSize: '13px',
            fontWeight: '500',
            zIndex: '999999',
            boxShadow: '0 3px 10px rgba(23, 162, 184, 0.3)',
            opacity: '0',
            transition: 'opacity 0.3s ease'
        });

        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 8px;">
                <span>✓</span>
                <span>Popup title saved</span>
            </div>
        `;

        document.body.appendChild(notification);

        // Fade in
        setTimeout(() => {
            notification.style.opacity = '1';
        }, 10);

        // Fade out and remove
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 2000);
    }

    showConfigChangeNotification(config) {
        const notification = TrueBDCUtils.createElement('div', {
            id: 'truebdc-config-change-notification'
        }, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: 'linear-gradient(135deg, #007bff, #0056b3)',
            color: 'white',
            padding: '12px 16px',
            borderRadius: '6px',
            fontSize: '13px',
            fontWeight: '500',
            zIndex: '999999',
            boxShadow: '0 3px 10px rgba(0, 123, 255, 0.3)',
            opacity: '0',
            transition: 'opacity 0.3s ease'
        });

        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 8px;">
                <span>📐</span>
                <span>Dimensions: ${config.width}×${config.height}</span>
            </div>
        `;

        document.body.appendChild(notification);

        // Fade in
        setTimeout(() => {
            notification.style.opacity = '1';
        }, 10);

        // Fade out and remove
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 2000);
    }

    async createPictureInPictureWindow(url, config, urlKey) {
        try {
            // Create Picture-in-Picture window (inherently always on top)
            const pipWindow = await window.documentPictureInPicture.requestWindow({
                width: config.width,
                height: config.height
            });

            if (!pipWindow) {
                throw new Error('Could not create Picture-in-Picture window');
            }

            // Set up the window content
            pipWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>eLeadCRM Weblink</title>
                    <style>
                        body, html {
                            margin: 0;
                            padding: 0;
                            width: 100%;
                            height: 100%;
                            overflow: hidden;
                        }
                        iframe {
                            width: 100%;
                            height: 100%;
                            border: none;
                        }
                    </style>
                </head>
                <body>
                    <iframe src="${url}"
                        title="eLeadCRM Weblink"
                        frameborder="0"
                        allow="clipboard-write"
                        sandbox="allow-downloads allow-forms allow-modals allow-orientation-lock allow-pointer-lock allow-popups allow-popups-to-escape-sandbox allow-presentation allow-same-origin allow-scripts allow-storage-access-by-user-activation">
                    </iframe>
                </body>
                </html>
            `);
            pipWindow.document.close();

            // Create a popup object that mimics the Chrome Windows API popup
            this.popup = {
                windowId: 'pip-' + Date.now(),
                tabId: null,
                closed: false,
                pipWindow: pipWindow
            };

            // Set up Picture-in-Picture specific handlers
            this.setupPictureInPictureHandlers(pipWindow, urlKey);

            TrueBDCUtils.log('Picture-in-Picture window created successfully', {
                urlKey: urlKey,
                config: config
            });

            TrueBDCUtils.logActivity('pip_window_created', {
                url: url,
                urlKey: urlKey,
                config: config
            });

        } catch (error) {
            TrueBDCUtils.error('Failed to create Picture-in-Picture window', error);

            // Fallback to regular popup window
            TrueBDCUtils.log('Falling back to regular popup window');
            const fallbackConfig = { ...config, alwaysOnTop: false };
            await this.createRegularPopupWindow(url, fallbackConfig, urlKey);
        }
    }

    setupPictureInPictureHandlers(pipWindow, urlKey) {
        // Handle window close
        pipWindow.addEventListener('pagehide', () => {
            if (this.popup && this.popup.pipWindow === pipWindow) {
                this.popup.closed = true;
                this.popup = null;
            }

            TrueBDCUtils.log('Picture-in-Picture window closed');
            TrueBDCUtils.logActivity('pip_window_closed', {
                urlKey: urlKey
            });
        });

        // Note: Picture-in-Picture windows don't support resize/move monitoring
        // They are inherently always on top and have fixed dimensions
        TrueBDCUtils.log('Picture-in-Picture handlers set up', {
            urlKey: urlKey
        });
    }

    async createRegularPopupWindow(url, config, urlKey) {
        // Send message to background script to create popup window with iframe URL
        const response = await chrome.runtime.sendMessage({
            action: 'createPopupWindow',
            url: url,
            config: config,
            urlKey: urlKey
        });

        if (response && response.success && response.window) {
            // Store reference to the popup window and tab
            this.popup = {
                windowId: response.window.id,
                tabId: response.window.tabs[0].id,
                window: response.window,
                // Add properties to mimic window.open() behavior
                closed: false,
                outerWidth: response.window.width,
                outerHeight: response.window.height,
                screenX: response.window.left,
                screenY: response.window.top
            };

            // Monitor window for changes
            this.monitorPopupWindow(this.popup);

            // Set up popup event handlers
            this.setupPopupHandlers(this.popup, urlKey);

            TrueBDCUtils.log('Regular popup window created successfully', {
                windowId: this.popup.windowId,
                tabId: this.popup.tabId,
                urlKey: urlKey,
                config: config
            });

            TrueBDCUtils.logActivity('popup_created', {
                url: url,
                urlKey: urlKey,
                config: config
            });
        } else {
            throw new Error(response?.error || 'Failed to create popup window');
        }
    }

    setupKeyListener() {
        this.keydownHandler = (event) => {
            // Check for Ctrl+Alt+9 (Windows) or Cmd+Option+9 (Mac)
            const isWindows = (event.ctrlKey && event.altKey && event.code === 'Digit9');
            const isMac = (event.metaKey && event.altKey && event.code === 'Digit9');
            
            if (isWindows || isMac) {
                TrueBDCUtils.log('Tab to popup shortcut detected', {
                    platform: isWindows ? 'Windows' : 'Mac',
                    ctrlKey: event.ctrlKey,
                    altKey: event.altKey,
                    metaKey: event.metaKey,
                    code: event.code
                });

                event.preventDefault();
                event.stopPropagation();

                this.convertTabToPopup();

                TrueBDCUtils.logActivity('tab_to_popup_triggered', {
                    platform: isWindows ? 'Windows' : 'Mac',
                    timestamp: new Date().toISOString()
                });

                return false;
            }
        };

        // Add event listener
        window.addEventListener('keydown', this.keydownHandler, true);
        document.addEventListener('keydown', this.keydownHandler, true);
    }

    isPictureInPictureSupported() {
        return 'documentPictureInPicture' in window &&
               typeof window.documentPictureInPicture.requestWindow === 'function';
    }

    async convertTabToPopup() {
        try {
            // Check if popup is already open to prevent duplicates
            if (this.popup && !this.popup.closed) {
                TrueBDCUtils.log('Popup already exists, focusing existing popup instead of creating new one');

                // Focus the existing popup window
                try {
                    await chrome.runtime.sendMessage({
                        action: 'focusWindow',
                        windowId: this.popup.windowId
                    });

                    // Show notification that popup already exists
                    this.showExistingPopupNotification();
                    return;
                } catch (error) {
                    TrueBDCUtils.log('Could not focus existing popup, it may have been closed');
                    // Reset popup reference and continue with creation
                    this.popup = null;
                }
            }

            // Show conversion notification
            this.showConversionNotification();

            // Detect iframe context
            const frameContext = TrueBDCUtils.getFrameContext();

            // Get iframe URL specifically - we want to extract just the iframe content
            const iframeUrl = window.location.href;
            const iframeTitle = document.title;
            const urlKey = this.getUrlKey(iframeUrl);

            TrueBDCUtils.log('Converting iframe to popup', {
                frameContext: frameContext,
                iframeUrl: iframeUrl,
                iframeTitle: iframeTitle,
                isIframe: frameContext.isIframe
            });

            // Verify we're in the correct iframe (weblinkToday.aspx)
            if (!frameContext.isIframe || !iframeUrl.includes('/elead_track/weblink/weblinkToday.aspx')) {
                throw new Error('Tab to Popup only works within the eLeadCRM weblink iframe');
            }

            // Get saved configuration for this URL or use default
            const savedConfig = this.savedConfigs.get(urlKey) || this.defaultConfig;
            this.currentUrlKey = urlKey;

            // Check if we should use Picture-in-Picture for always on top
            if (savedConfig.alwaysOnTop && this.isPictureInPictureSupported()) {
                TrueBDCUtils.log('Using Picture-in-Picture for always-on-top functionality');
                await this.createPictureInPictureWindow(iframeUrl, savedConfig, urlKey);
                return;
            }

            TrueBDCUtils.log('Using popup configuration', {
                urlKey: urlKey,
                config: savedConfig
            });

            TrueBDCUtils.log('Creating popup window from iframe via background script', {
                iframeUrl: iframeUrl,
                config: savedConfig
            });

            // Create regular popup window
            await this.createRegularPopupWindow(iframeUrl, savedConfig, urlKey);

            if (this.popup) {
                // Set up popup event handlers
                this.setupPopupHandlers(this.popup, urlKey);



                // Show success message
                setTimeout(() => {
                    this.showResizeTooltip();
                }, 1000);

                // Since we extracted the iframe content to a popup,
                // we can optionally close the original tab
                setTimeout(() => {
                    try {
                        // Close the top window since we've extracted the iframe content
                        window.top.close();
                    } catch (error) {
                        TrueBDCUtils.log('Cannot close top window from iframe - user can close manually');
                        // Don't force close if there are restrictions
                    }
                }, 500);

            } else {
                throw new Error('Failed to open popup window - popup blocked?');
            }

        } catch (error) {
            TrueBDCUtils.error('Error converting tab to popup', error);
            this.showErrorNotification('Failed to convert tab to popup. Please check popup blocker settings.');
        }
    }

    monitorPopupWindow(popup) {
        // Monitor window changes via background script
        const updatePopupProperties = async () => {
            try {
                const response = await chrome.runtime.sendMessage({
                    action: 'getWindowInfo',
                    windowId: popup.windowId
                });

                if (response && response.success && response.window) {
                    // Update popup properties to mimic window.open() behavior
                    popup.outerWidth = response.window.width;
                    popup.outerHeight = response.window.height;
                    popup.screenX = response.window.left;
                    popup.screenY = response.window.top;
                    popup.window = response.window;
                } else {
                    popup.closed = true;
                }
            } catch (error) {
                popup.closed = true;
            }
        };

        // Update properties periodically
        const monitorInterval = setInterval(() => {
            if (popup.closed) {
                clearInterval(monitorInterval);
                return;
            }
            updatePopupProperties();
        }, 500);

        // Listen for window removal messages
        const messageListener = (message) => {
            if (message.action === 'windowRemoved' && message.windowId === popup.windowId) {
                popup.closed = true;
                clearInterval(monitorInterval);
                chrome.runtime.onMessage.removeListener(messageListener);

                // Reset the popup reference in the TabToPopup instance
                if (this.popup && this.popup.windowId === popup.windowId) {
                    this.popup = null;
                    TrueBDCUtils.log('Popup reference cleared after window removal');
                }
            }
        };
        chrome.runtime.onMessage.addListener(messageListener);

        // Initial property update
        updatePopupProperties();
    }

    setupPopupHandlers(popup, urlKey) {
        // Enhanced handler to track both position and size changes
        let changeTimeout;
        let autoRefreshPaused = false;

        const pauseAutoRefreshDisplay = () => {
            // Only pause the countdown display, NOT the actual refresh functionality
            try {
                if (popup && popup.document) {
                    const timerDisplay = popup.document.getElementById('autoRefreshTimerDisplay');
                    if (timerDisplay) {
                        // Hide timer display during window manipulation
                        timerDisplay.style.opacity = '0.3';
                        timerDisplay.style.pointerEvents = 'none';
                        TrueBDCUtils.log('Auto-refresh display dimmed during window manipulation');
                    }
                }
            } catch (error) {
                TrueBDCUtils.log('Could not access popup document for timer display');
            }
        };

        const resumeAutoRefreshDisplay = () => {
            // Resume the countdown display when user stops interacting
            try {
                if (popup && popup.document) {
                    const timerDisplay = popup.document.getElementById('autoRefreshTimerDisplay');
                    if (timerDisplay) {
                        // Restore timer display visibility
                        timerDisplay.style.opacity = '1';
                        timerDisplay.style.pointerEvents = 'auto';
                        TrueBDCUtils.log('Auto-refresh display restored after window manipulation');
                    }
                }
            } catch (error) {
                TrueBDCUtils.log('Could not access popup document for timer display');
            }
        };

        const handleConfigChange = (isResizeEvent = false) => {
            clearTimeout(changeTimeout);
            clearTimeout(this.saveTimeout);
            clearTimeout(this.resizeEndTimeout);

            if (isResizeEvent) {
                this.isResizing = true;
                pauseAutoRefreshDisplay();
            }

            // Debounce to avoid excessive saves during dragging/resizing
            changeTimeout = setTimeout(() => {
                if (popup && !popup.closed) {
                    const newConfig = {
                        width: popup.outerWidth,
                        height: popup.outerHeight,
                        left: popup.screenX,
                        top: popup.screenY
                    };

                    // Only save if configuration actually changed
                    const currentConfig = this.savedConfigs.get(urlKey) || this.defaultConfig;
                    const hasChanged =
                        newConfig.width !== currentConfig.width ||
                        newConfig.height !== currentConfig.height ||
                        newConfig.left !== currentConfig.left ||
                        newConfig.top !== currentConfig.top;

                    if (hasChanged) {
                        this.savedConfigs.set(urlKey, newConfig);

                        // Debounce the actual save to storage
                        this.saveTimeout = setTimeout(() => {
                            this.saveConfigs();
                        }, 500);

                        TrueBDCUtils.log('Popup configuration changed', {
                            urlKey: urlKey,
                            config: newConfig,
                            changed: {
                                size: newConfig.width !== currentConfig.width || newConfig.height !== currentConfig.height,
                                position: newConfig.left !== currentConfig.left || newConfig.top !== currentConfig.top
                            }
                        });

                        // Show visual feedback
                        this.showConfigChangeNotification(newConfig);
                    }
                }

                // Set up resize end detection to resume auto-refresh display
                if (isResizeEvent) {
                    this.resizeEndTimeout = setTimeout(() => {
                        this.isResizing = false;
                        resumeAutoRefreshDisplay();
                    }, 500); // Resume display 0.5 seconds after resize stops
                }
            }, 300); // Short delay to capture final position/size
        };

        // Monitor window changes using Chrome Windows API (more reliable than popup properties)
        let lastConfig = null;
        let windowMonitor = null;

        const startWindowMonitoring = async () => {
            try {
                // Get initial config from Chrome Windows API
                const windowInfo = await chrome.windows.get(popup.windowId);
                lastConfig = {
                    width: windowInfo.width,
                    height: windowInfo.height,
                    left: windowInfo.left,
                    top: windowInfo.top
                };

                windowMonitor = setInterval(async () => {
                    try {
                        // Use Chrome Windows API instead of popup properties (more reliable)
                        const windowInfo = await chrome.windows.get(popup.windowId);

                        const currentConfig = {
                            width: windowInfo.width,
                            height: windowInfo.height,
                            left: windowInfo.left,
                            top: windowInfo.top
                        };

                        // Check for size changes (resize)
                        const sizeChanged = currentConfig.width !== lastConfig.width ||
                                           currentConfig.height !== lastConfig.height;

                        // Check for position changes (move)
                        const positionChanged = currentConfig.left !== lastConfig.left ||
                                               currentConfig.top !== lastConfig.top;

                        if (sizeChanged || positionChanged) {
                            lastConfig = currentConfig;
                            handleConfigChange(sizeChanged); // Pass true if it's a resize
                        }
                    } catch (error) {
                        // Window was closed, stop monitoring
                        if (windowMonitor) {
                            clearInterval(windowMonitor);
                            windowMonitor = null;
                        }
                    }
                }, 300);
            } catch (error) {
                TrueBDCUtils.error('Error starting window monitoring', error);
            }
        };

        // Start monitoring
        startWindowMonitoring();

        // Handle popup close via message listener (already set up in monitorPopupWindow)
        const closeListener = (message) => {
            if (message.action === 'windowRemoved' && message.windowId === popup.windowId) {
                if (windowMonitor) {
                    clearInterval(windowMonitor);
                    windowMonitor = null;
                }
                clearTimeout(changeTimeout);
                clearTimeout(this.saveTimeout);

                TrueBDCUtils.log('Popup closing');
                TrueBDCUtils.logActivity('popup_closed', {
                    urlKey: urlKey,
                    finalConfig: this.savedConfigs.get(urlKey)
                });

                // Reset the popup reference in the TabToPopup instance
                if (this.popup && this.popup.windowId === popup.windowId) {
                    this.popup = null;
                    TrueBDCUtils.log('Popup reference cleared in setupPopupHandlers');
                }

                chrome.runtime.onMessage.removeListener(closeListener);
            }
        };
        chrome.runtime.onMessage.addListener(closeListener);
    }



    getUrlKey(url) {
        // Create a key for storing popup configurations (size + position) based on URL pattern
        try {
            const urlObj = new URL(url);
            const pathname = urlObj.pathname;
            
            // Extract meaningful part of the path
            if (pathname.includes('weblinkToday.aspx')) {
                return 'weblink';
            } else if (pathname.includes('reports')) {
                return 'reports';
            } else if (pathname.includes('NewProspects')) {
                return 'prospects';
            } else if (pathname.includes('elead_mail')) {
                return 'mail';
            } else {
                return 'default';
            }
        } catch (error) {
            return 'default';
        }
    }

    showConversionNotification() {
        const frameContext = TrueBDCUtils.getFrameContext();

        const notification = TrueBDCUtils.createElement('div', {
            id: 'truebdc-popup-notification'
        }, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: 'linear-gradient(135deg, #007bff, #0056b3)',
            color: 'white',
            padding: '15px 20px',
            borderRadius: '8px',
            fontSize: '14px',
            fontWeight: '500',
            zIndex: '999999',
            boxShadow: '0 4px 12px rgba(0, 123, 255, 0.3)',
            opacity: '0',
            transition: 'opacity 0.3s ease'
        });

        const message = frameContext.isIframe ?
            'Converting iframe content to popup window...' :
            'Converting tab to popup window...';

        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px;">
                <div class="truebdc-spinner" style="width: 16px; height: 16px; border-width: 2px;"></div>
                <span>${message}</span>
            </div>
        `;

        document.body.appendChild(notification);

        // Fade in
        setTimeout(() => {
            notification.style.opacity = '1';
        }, 10);

        // Remove after conversion
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }

    showExistingPopupNotification() {
        const notification = TrueBDCUtils.createElement('div', {
            id: 'truebdc-existing-popup-notification'
        }, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: 'linear-gradient(135deg, #28a745, #1e7e34)',
            color: 'white',
            padding: '15px 20px',
            borderRadius: '8px',
            fontSize: '14px',
            fontWeight: '500',
            zIndex: '999999',
            boxShadow: '0 4px 12px rgba(40, 167, 69, 0.3)',
            opacity: '0',
            transition: 'opacity 0.3s ease'
        });

        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px;">
                <span>✓</span>
                <span>Popup already exists - focusing existing window</span>
            </div>
        `;

        document.body.appendChild(notification);

        // Fade in
        setTimeout(() => {
            notification.style.opacity = '1';
        }, 10);

        // Remove after showing
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 2000);
    }

    showResizeTooltip() {
        const tooltip = TrueBDCUtils.createElement('div', {
            id: 'truebdc-resize-tooltip'
        }, {
            position: 'fixed',
            bottom: '20px',
            right: '20px',
            background: 'linear-gradient(135deg, #28a745, #20c997)',
            color: 'white',
            padding: '12px 16px',
            borderRadius: '8px',
            fontSize: '13px',
            zIndex: '999999',
            boxShadow: '0 4px 12px rgba(40, 167, 69, 0.3)',
            opacity: '0',
            transition: 'opacity 0.3s ease',
            maxWidth: '300px'
        });

        tooltip.innerHTML = `
            <div>
                <strong>💡 Tip:</strong> Resize this window to your preferred size. 
                The size will be remembered for next time!
            </div>
        `;

        document.body.appendChild(tooltip);

        // Fade in
        setTimeout(() => {
            tooltip.style.opacity = '1';
        }, 100);

        // Fade out after 5 seconds
        setTimeout(() => {
            tooltip.style.opacity = '0';
            setTimeout(() => {
                if (tooltip.parentNode) {
                    tooltip.parentNode.removeChild(tooltip);
                }
            }, 300);
        }, 5000);
    }

    showErrorNotification(message) {
        const notification = TrueBDCUtils.createElement('div', {
            id: 'truebdc-error-notification'
        }, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: 'linear-gradient(135deg, #dc3545, #c82333)',
            color: 'white',
            padding: '15px 20px',
            borderRadius: '8px',
            fontSize: '14px',
            fontWeight: '500',
            zIndex: '999999',
            boxShadow: '0 4px 12px rgba(220, 53, 69, 0.3)',
            opacity: '0',
            transition: 'opacity 0.3s ease',
            maxWidth: '350px'
        });

        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px;">
                <span>⚠️</span>
                <span>${message}</span>
            </div>
        `;

        document.body.appendChild(notification);

        // Fade in
        setTimeout(() => {
            notification.style.opacity = '1';
        }, 10);

        // Remove after 5 seconds
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 5000);
    }

    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        TrueBDCUtils.log('Tab to Popup settings updated', newSettings);
    }

    onPageChange() {
        // Handle page changes in SPAs
        if (this.isSupportedPage()) {
            if (!this.isActive) {
                this.init();
            }
        } else {
            if (this.isActive) {
                this.destroy();
            }
        }
    }

    destroy() {
        try {
            // Remove event listeners
            if (this.keydownHandler) {
                window.removeEventListener('keydown', this.keydownHandler, true);
                document.removeEventListener('keydown', this.keydownHandler, true);
                this.keydownHandler = null;
            }

            // Close popup if open
            if (this.popup && !this.popup.closed) {
                this.popup.close();
                this.popup = null;
            }

            this.isActive = false;
            
            TrueBDCUtils.log('Tab to Popup Converter destroyed');
            TrueBDCUtils.logActivity('tab_to_popup_destroyed');
        } catch (error) {
            TrueBDCUtils.error('Error destroying Tab to Popup Converter', error);
        }
    }

    // Static method to check if script should be available
    static isAvailable() {
        const url = window.location.href;
        return /eleadcrm\.com|vinsolutions\.com/i.test(url);
    }

    // Get current status
    getStatus() {
        return {
            isActive: this.isActive,
            isSupported: this.isSupportedPage(),
            hasKeyListener: !!this.keydownHandler,
            savedSizesCount: this.savedSizes.size,
            popupOpen: this.popup && !this.popup.closed
        };
    }
}

// Make class globally available
window.TabToPopup = TabToPopup;
