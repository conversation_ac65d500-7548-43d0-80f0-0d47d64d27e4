{"buyMeACoffee": {"message": "☕ 커피 한 잔 사주기"}, "contextMenu": {"message": "🖱️ 컨텍스트 메뉴"}, "extensionDescription": {"message": "모든 웹페이지를 원활한 멀티태스킹과 생산성 향상을 위한 플로팅 항상 위에 표시 창으로 변환"}, "extensionName": {"message": "컴패니언 윈도우 | 항상 위에"}, "floatingButton": {"message": "••• 플로팅 버튼"}, "github": {"message": "🌐 GitHub"}, "inPage": {"message": "페이지 내"}, "issuesAndSuggestions": {"message": "🤔 문제점 및 제안"}, "keyboardShortcuts": {"message": "⌨️ 키보드 단축키"}, "leaveAReview": {"message": "🌟 리뷰 남기기"}, "off": {"message": "끄기"}, "on": {"message": "켜기"}, "onLink": {"message": "링크에서"}, "openInCompanionWindow": {"message": "컴패니언 윈도우에서 열기"}, "openLinkInCompanionWindow": {"message": "링크를 컴패니언 윈도우에서 열기"}, "options": {"message": "⚙️ 옵션"}, "reloadTabOrBrowser": {"message": "Companion Window가 방금 설치되거나 업데이트되었습니다. 탭을 다시 로드하거나 브라우저를 재시작하세요."}, "reportIssue": {"message": "🐛 문제 신고하기"}, "support": {"message": "❤️ 지원"}, "toggleCompanionWindow": {"message": "컴패니언 윈도우 전환"}, "unsupportedHost": {"message": "Companion Window: 이 호스트에서 지원되지 않습니다"}}