{"buyMeACoffee": {"message": "☕ Угостить кофе"}, "contextMenu": {"message": "🖱️ Контекстное меню"}, "extensionDescription": {"message": "Превратите любую веб-страницу в плавающее окно поверх всех окон для удобной многозадачности и повышенной продуктивности"}, "extensionName": {"message": "Окно-компаньон | Поверх всех окон"}, "floatingButton": {"message": "••• Плавающая кнопка"}, "github": {"message": "🌐 GitHub"}, "inPage": {"message": "На странице"}, "issuesAndSuggestions": {"message": "🤔 Проблемы и предложения"}, "keyboardShortcuts": {"message": "⌨️ Горячие клавиши"}, "leaveAReview": {"message": "🌟 Оставить отзыв"}, "off": {"message": "Выключено"}, "on": {"message": "Включено"}, "onLink": {"message": "На ссылке"}, "openInCompanionWindow": {"message": "Открыть в окне-компаньоне"}, "openLinkInCompanionWindow": {"message": "Открыть ссылку в окне-компаньоне"}, "options": {"message": "⚙️ Настройки"}, "reloadTabOrBrowser": {"message": "Companion Window только что был установлен или обновлен. Пожалуйста, перезагрузите вкладку или перезапустите браузер."}, "reportIssue": {"message": "🐛 Сообщить о проблеме"}, "support": {"message": "❤️ Поддержка"}, "toggleCompanionWindow": {"message": "Переключить окно-компаньон"}, "unsupportedHost": {"message": "Companion Window: не поддерживается на этом хосте"}}