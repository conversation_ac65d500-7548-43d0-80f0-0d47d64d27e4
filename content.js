// TrueBDC CRM Automation Suite - Main Content Script

// Prevent multiple injections
if (window.trueBDCContentLoaded) {
    console.log('[TrueBDC] Content script already loaded, skipping');
} else {
    window.trueBDCContentLoaded = true;

// Iframe detection and context management
const isInIframe = window !== window.top;
const isMainFrame = window === window.top;
const frameContext = {
    isIframe: isInIframe,
    isMainFrame: isMainFrame,
    frameId: isInIframe ? Math.random().toString(36).substr(2, 9) : 'main',
    url: window.location.href,
    origin: window.location.origin
};

class TrueBDCContentScript {
    constructor() {
        this.settings = {};
        this.scriptStates = {};
        this.crmSystem = TrueBDCUtils.detectCRMSystem();
        this.initialized = false;
        this.scripts = {};
        this.frameContext = frameContext;
        this.iframeManager = null;

        this.init();
    }

    async init() {
        try {
            // Check if TrueBDCUtils is available
            if (typeof TrueBDCUtils === 'undefined') {
                console.error('[TrueBDC] TrueBDCUtils not available, waiting...');
                setTimeout(() => this.init(), 100);
                return;
            }

            TrueBDCUtils.log('Initializing TrueBDC Content Script', {
                url: window.location.href,
                crm: this.crmSystem,
                frameContext: this.frameContext
            });

            // Initialize iframe management
            this.setupIframeManagement();

            // Load settings and script states
            await this.loadSettings();
            await this.loadScriptStates();

            // Initialize all scripts
            this.initializeScripts();

            // Set up message listener
            this.setupMessageListener();

            // Set up page change detection
            this.setupPageChangeDetection();

            // Set up iframe communication if in main frame
            if (this.frameContext.isMainFrame) {
                this.setupIframeCommunication();
            }

            this.initialized = true;
            TrueBDCUtils.log('TrueBDC Content Script initialized successfully', {
                frameContext: this.frameContext
            });

            // Log activity
            TrueBDCUtils.logActivity('content_script_initialized', {
                crm: this.crmSystem,
                frameContext: this.frameContext,
                scriptsEnabled: Object.keys(this.scriptStates).filter(key => this.scriptStates[key]).length
            });

            // Report frame context to background script
            this.reportFrameContext();

        } catch (error) {
            TrueBDCUtils.error('Failed to initialize content script', error);
        }
    }

    async loadSettings() {
        try {
            this.settings = await TrueBDCUtils.getSettings();
            TrueBDCUtils.log('Settings loaded', this.settings);
        } catch (error) {
            TrueBDCUtils.error('Failed to load settings', error);
            this.settings = {};
        }
    }

    async loadScriptStates() {
        try {
            const scriptNames = [
                'dynamicTabTitle',
                'bypassRefresh',
                'clickToCall',
                'tabToPopup',
                'autoRefresh',
                'callingText',
                'autoNavigation',
                'autoCloseReleaseNotes'
            ];

            for (const scriptName of scriptNames) {
                this.scriptStates[scriptName] = await TrueBDCUtils.getScriptState(scriptName);
            }

            TrueBDCUtils.log('Script states loaded', this.scriptStates);
        } catch (error) {
            TrueBDCUtils.error('Failed to load script states', error);
        }
    }

    initializeScripts() {
        // Get iframe-aware settings
        const iframeSettings = this.iframeManager ? this.iframeManager.getIframeSettings() : this.settings;

        // Initialize each script based on its enabled state and iframe context
        if (this.scriptStates.dynamicTabTitle && window.DynamicTabTitle &&
            (!this.iframeManager || this.iframeManager.shouldRunScript('dynamicTabTitle'))) {
            this.scripts.dynamicTabTitle = new DynamicTabTitle(iframeSettings);
        }

        if (this.scriptStates.bypassRefresh && window.BypassRefresh &&
            (!this.iframeManager || this.iframeManager.shouldRunScript('bypassRefresh'))) {
            this.scripts.bypassRefresh = new BypassRefresh(iframeSettings);
        }

        if (this.scriptStates.clickToCall && window.ClickToCall &&
            (!this.iframeManager || this.iframeManager.shouldRunScript('clickToCall'))) {
            this.scripts.clickToCall = new ClickToCall(iframeSettings);
        }

        if (this.scriptStates.tabToPopup && window.TabToPopup &&
            (!this.iframeManager || this.iframeManager.shouldRunScript('tabToPopup'))) {
            this.scripts.tabToPopup = new TabToPopup(iframeSettings);
        }

        if (this.scriptStates.autoRefresh && window.AutoRefresh &&
            (!this.iframeManager || this.iframeManager.shouldRunScript('autoRefresh'))) {
            this.scripts.autoRefresh = new AutoRefresh(iframeSettings);
        }

        if (this.scriptStates.callingText && window.CallingText &&
            (!this.iframeManager || this.iframeManager.shouldRunScript('callingText'))) {
            this.scripts.callingText = new CallingText(iframeSettings);
        }

        if (this.scriptStates.autoNavigation && window.AutoNavigation &&
            (!this.iframeManager || this.iframeManager.shouldRunScript('autoNavigation'))) {
            this.scripts.autoNavigation = new AutoNavigation(iframeSettings);
        }

        if (this.scriptStates.autoCloseReleaseNotes && window.AutoCloseReleaseNotes &&
            (!this.iframeManager || this.iframeManager.shouldRunScript('autoCloseReleaseNotes'))) {
            this.scripts.autoCloseReleaseNotes = new AutoCloseReleaseNotes(iframeSettings);
        }

        TrueBDCUtils.log('Scripts initialized', {
            scripts: Object.keys(this.scripts),
            frameContext: this.frameContext
        });

        // Notify main frame if we're in an iframe
        if (this.frameContext.isIframe && Object.keys(this.scripts).length > 0) {
            this.notifyMainFrame('SCRIPT_ACTIVATED', {
                scripts: Object.keys(this.scripts),
                frameId: this.frameContext.frameId,
                url: this.frameContext.url
            });
        }
    }

    setupMessageListener() {
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true; // Keep message channel open
        });
    }

    async handleMessage(message, sender, sendResponse) {
        try {
            switch (message.action) {
                case 'toggleScript':
                    await this.toggleScript(message.script, message.enabled);
                    sendResponse({ success: true });
                    break;

                case 'updateSettings':
                    await this.updateSettings(message.settings);
                    sendResponse({ success: true });
                    break;

                case 'settingsUpdated':
                    this.settings = message.data;
                    this.updateAllScripts();
                    sendResponse({ success: true });
                    break;

                case 'scriptToggled':
                    const { script, enabled } = message.data;
                    this.scriptStates[script] = enabled;
                    await this.toggleScript(script, enabled);
                    sendResponse({ success: true });
                    break;

                case 'storageChanged':
                    await this.handleStorageChange(message.data);
                    sendResponse({ success: true });
                    break;

                case 'getPageInfo':
                    sendResponse({
                        success: true,
                        data: {
                            url: window.location.href,
                            title: document.title,
                            crm: this.crmSystem,
                            initialized: this.initialized
                        }
                    });
                    break;

                default:
                    TrueBDCUtils.log('Unknown message action:', message.action);
                    sendResponse({ success: false, error: 'Unknown action' });
            }
        } catch (error) {
            TrueBDCUtils.error('Error handling message', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    reportFrameContext() {
        // Report frame context to background script for monitoring
        TrueBDCUtils.sendMessage('frameContextReport', {
            frameContext: this.frameContext,
            crmSystem: this.crmSystem,
            scriptsEnabled: Object.keys(this.scriptStates).filter(key => this.scriptStates[key]),
            initialized: this.initialized
        });
    }

    notifyMainFrame(action, data) {
        // Only send messages if we're in an iframe
        if (!this.frameContext.isIframe) return;

        try {
            window.parent.postMessage({
                type: 'TRUEBDC_IFRAME_MESSAGE',
                action: action,
                frameId: this.frameContext.frameId,
                url: this.frameContext.url,
                ...data
            }, '*');
        } catch (error) {
            TrueBDCUtils.error('Failed to notify main frame', error);
        }
    }

    async toggleScript(scriptName, enabled) {
        try {
            this.scriptStates[scriptName] = enabled;

            if (enabled) {
                // Check if script should run in current frame context
                if (this.iframeManager && !this.iframeManager.shouldRunScript(scriptName)) {
                    TrueBDCUtils.log(`Script ${scriptName} skipped in iframe context`);
                    return;
                }

                // Get iframe-aware settings
                const iframeSettings = this.iframeManager ? this.iframeManager.getIframeSettings() : this.settings;

                // Enable script
                switch (scriptName) {
                    case 'dynamicTabTitle':
                        if (window.DynamicTabTitle) {
                            this.scripts.dynamicTabTitle = new DynamicTabTitle(iframeSettings);
                        }
                        break;
                    case 'bypassRefresh':
                        if (window.BypassRefresh) {
                            this.scripts.bypassRefresh = new BypassRefresh(iframeSettings);
                        }
                        break;
                    case 'clickToCall':
                        if (window.ClickToCall) {
                            this.scripts.clickToCall = new ClickToCall(iframeSettings);
                        }
                        break;
                    case 'tabToPopup':
                        if (window.TabToPopup) {
                            this.scripts.tabToPopup = new TabToPopup(iframeSettings);
                        }
                        break;
                    case 'autoRefresh':
                        if (window.AutoRefresh) {
                            this.scripts.autoRefresh = new AutoRefresh(iframeSettings);
                        }
                        break;
                    case 'callingText':
                        if (window.CallingText) {
                            this.scripts.callingText = new CallingText(iframeSettings);
                        }
                        break;
                }
            } else {
                // Disable script
                if (this.scripts[scriptName] && this.scripts[scriptName].destroy) {
                    this.scripts[scriptName].destroy();
                }
                delete this.scripts[scriptName];
            }

            TrueBDCUtils.log(`Script ${scriptName} ${enabled ? 'enabled' : 'disabled'}`);
            
            // Log activity
            TrueBDCUtils.logActivity('script_toggled', {
                script: scriptName,
                enabled: enabled
            });

        } catch (error) {
            TrueBDCUtils.error(`Failed to toggle script ${scriptName}`, error);
        }
    }

    async updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        this.updateAllScripts();
        
        TrueBDCUtils.log('Settings updated', this.settings);
        
        // Log activity
        TrueBDCUtils.logActivity('settings_updated', {
            settingsKeys: Object.keys(newSettings)
        });
    }

    updateAllScripts() {
        // Update settings for all active scripts
        Object.values(this.scripts).forEach(script => {
            if (script && script.updateSettings) {
                script.updateSettings(this.settings);
            }
        });
    }

    async handleStorageChange(changes) {
        // Handle storage changes from background script
        if (changes.settings) {
            this.settings = changes.settings.newValue;
            this.updateAllScripts();
        }

        // Handle script state changes
        Object.keys(changes).forEach(key => {
            if (key.startsWith('script_')) {
                const scriptName = key.replace('script_', '');
                const enabled = changes[key].newValue;
                this.scriptStates[scriptName] = enabled;
                this.toggleScript(scriptName, enabled);
            }
        });
    }

    setupPageChangeDetection() {
        // Detect page changes in SPAs
        let currentUrl = window.location.href;
        
        const checkForUrlChange = () => {
            if (window.location.href !== currentUrl) {
                currentUrl = window.location.href;
                this.handlePageChange();
            }
        };

        // Check for URL changes periodically
        setInterval(checkForUrlChange, 1000);

        // Also listen for popstate events
        window.addEventListener('popstate', () => {
            setTimeout(() => this.handlePageChange(), 100);
        });

        // Listen for pushstate/replacestate
        const originalPushState = history.pushState;
        const originalReplaceState = history.replaceState;

        history.pushState = function(...args) {
            originalPushState.apply(history, args);
            setTimeout(() => window.trueBDCContent.handlePageChange(), 100);
        };

        history.replaceState = function(...args) {
            originalReplaceState.apply(history, args);
            setTimeout(() => window.trueBDCContent.handlePageChange(), 100);
        };
    }

    handlePageChange() {
        TrueBDCUtils.log('Page changed', { url: window.location.href });
        
        // Reinitialize scripts that need to respond to page changes
        Object.values(this.scripts).forEach(script => {
            if (script && script.onPageChange) {
                script.onPageChange();
            }
        });

        // Log activity
        TrueBDCUtils.logActivity('page_changed', {
            newUrl: window.location.href
        });
    }

    setupIframeManagement() {
        // Create iframe manager for handling iframe-specific logic
        this.iframeManager = {
            isIframe: this.frameContext.isIframe,
            isMainFrame: this.frameContext.isMainFrame,
            frameId: this.frameContext.frameId,

            // Check if current context should run specific scripts
            shouldRunScript: (scriptName) => {
                // Some scripts should only run in main frame
                const mainFrameOnly = ['dynamicTabTitle'];
                if (mainFrameOnly.includes(scriptName) && this.frameContext.isIframe) {
                    return false;
                }

                // Some scripts should run in all frames (including iframes)
                const allFrames = ['clickToCall', 'bypassRefresh', 'callingText', 'autoRefresh', 'tabToPopup'];
                if (allFrames.includes(scriptName)) {
                    return true;
                }

                return true; // Default: run in all contexts
            },

            // Get iframe-specific settings
            getIframeSettings: () => {
                return {
                    ...this.settings,
                    frameContext: this.frameContext
                };
            }
        };

        TrueBDCUtils.log('Iframe manager initialized', this.iframeManager);
    }

    setupIframeCommunication() {
        // Only run in main frame
        if (!this.frameContext.isMainFrame) return;

        // Listen for iframe messages
        window.addEventListener('message', (event) => {
            // Verify origin for security
            if (!event.origin.includes('eleadcrm.com') && !event.origin.includes('vinsolutions.com')) {
                return;
            }

            if (event.data && event.data.type === 'TRUEBDC_IFRAME_MESSAGE') {
                this.handleIframeMessage(event.data, event.source);
            }
        });

        // Monitor for new iframes
        this.monitorIframes();

        TrueBDCUtils.log('Iframe communication setup complete');
    }

    handleIframeMessage(data, source) {
        TrueBDCUtils.log('Received iframe message', data);

        switch (data.action) {
            case 'SCRIPT_ACTIVATED':
                TrueBDCUtils.logActivity('iframe_script_activated', {
                    script: data.script,
                    frameId: data.frameId,
                    url: data.url
                });
                break;

            case 'SETTINGS_REQUEST':
                // Send settings to iframe
                source.postMessage({
                    type: 'TRUEBDC_SETTINGS_RESPONSE',
                    settings: this.settings,
                    scriptStates: this.scriptStates
                }, '*');
                break;

            case 'LOG_ACTIVITY':
                TrueBDCUtils.logActivity(data.activity, data.details);
                break;
        }
    }

    monitorIframes() {
        // Watch for dynamically added iframes
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE && node.tagName === 'IFRAME') {
                        this.handleNewIframe(node);
                    }
                });
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        // Also check existing iframes
        document.querySelectorAll('iframe').forEach(iframe => {
            this.handleNewIframe(iframe);
        });
    }

    handleNewIframe(iframe) {
        TrueBDCUtils.log('New iframe detected', {
            src: iframe.src,
            id: iframe.id,
            className: iframe.className
        });

        // Log iframe for debugging
        TrueBDCUtils.logActivity('iframe_detected', {
            src: iframe.src,
            id: iframe.id,
            className: iframe.className
        });
    }

    destroy() {
        // Clean up all scripts
        Object.values(this.scripts).forEach(script => {
            if (script && script.destroy) {
                script.destroy();
            }
        });

        this.scripts = {};
        this.initialized = false;

        TrueBDCUtils.log('TrueBDC Content Script destroyed');
    }
}

// Initialize content script when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.trueBDCContent = new TrueBDCContentScript();
    });
} else {
    window.trueBDCContent = new TrueBDCContentScript();
}

} // End of guard
