// TrueBDC CRM Automation Suite - Background Service Worker

class TrueBDCBackground {
    constructor() {
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeDefaultSettings();
    }

    setupEventListeners() {
        // Extension installation/update
        chrome.runtime.onInstalled.addListener((details) => {
            this.handleInstallation(details);
        });

        // Message handling from content scripts and popup
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true; // Keep message channel open for async responses
        });

        // Tab updates
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            this.handleTabUpdate(tabId, changeInfo, tab);
        });

        // Storage changes
        chrome.storage.onChanged.addListener((changes, namespace) => {
            this.handleStorageChange(changes, namespace);
        });

        // Window removal listener for popup management
        chrome.windows.onRemoved.addListener((windowId) => {
            this.notifyAllCRMTabs('windowRemoved', { windowId });
        });
    }

    async handleInstallation(details) {
        console.log('TrueBDC Extension installed/updated:', details);

        if (details.reason === 'install') {
            // First time installation
            await this.initializeDefaultSettings();
            
            // Show welcome notification
            chrome.notifications.create({
                type: 'basic',
                iconUrl: 'icons/icon48.png',
                title: 'TrueBDC CRM Automation',
                message: 'Extension installed successfully! Click the extension icon to get started.'
            });
        } else if (details.reason === 'update') {
            // Extension updated
            console.log('Extension updated to version:', chrome.runtime.getManifest().version);
        }
    }

    async initializeDefaultSettings() {
        try {
            const result = await chrome.storage.local.get('settings');
            
            if (!result.settings) {
                const defaultSettings = {
                    dealershipName: '',
                    agentName: 'Rep',
                    // Airtable Integration - Temporarily Disabled
                    // airtableApiKey: '',
                    // airtableBaseId: 'appexR9tFKGHjSWNE',
                    refreshInterval: 5,
                    enableNotifications: true,
                    debugMode: false,
                    // Popup window settings
                    popupWidth: 1200,
                    popupHeight: 800,
                    popupAlwaysOnTop: false
                };

                await chrome.storage.local.set({ 
                    settings: defaultSettings,
                    profiles: [],
                    version: chrome.runtime.getManifest().version
                });

                console.log('Default settings initialized');
            }

            // Initialize default script states
            const scripts = [
                'dynamicTabTitle',
                'bypassRefresh',
                'clickToCall',
                'tabToPopup',
                'autoRefresh',
                'callingText'
            ];

            for (const script of scripts) {
                const scriptResult = await chrome.storage.local.get(`script_${script}`);
                if (scriptResult[`script_${script}`] === undefined) {
                    await chrome.storage.local.set({ [`script_${script}`]: false });
                }
            }

        } catch (error) {
            console.error('Error initializing default settings:', error);
        }
    }

    async handleMessage(message, sender, sendResponse) {
        try {
            switch (message.action) {
                case 'getSettings':
                    const settings = await this.getSettings();
                    sendResponse({ success: true, data: settings });
                    break;

                case 'updateSettings':
                    await this.updateSettings(message.settings);
                    sendResponse({ success: true });
                    break;

                case 'getScriptState':
                    const state = await this.getScriptState(message.script);
                    sendResponse({ success: true, enabled: state });
                    break;

                case 'toggleScript':
                    await this.toggleScript(message.script, message.enabled);
                    sendResponse({ success: true });
                    break;

                case 'showNotification':
                    await this.showNotification(message.title, message.message, message.type);
                    sendResponse({ success: true });
                    break;

                case 'logActivity':
                    await this.logActivity(message.activity, message.data);
                    sendResponse({ success: true });
                    break;

                case 'checkCRMCompatibility':
                    const compatibility = await this.checkCRMCompatibility(sender.tab.url);
                    sendResponse({ success: true, compatible: compatibility });
                    break;

                case 'iframeDetected':
                    await this.handleIframeDetection(message.data, sender);
                    sendResponse({ success: true });
                    break;

                case 'frameContextReport':
                    await this.handleFrameContextReport(message.data, sender);
                    sendResponse({ success: true });
                    break;

                case 'createPopupWindow':
                    const popupResult = await this.createPopupWindow(message.url, message.config, message.urlKey);
                    sendResponse(popupResult);
                    break;

                case 'getWindowInfo':
                    const windowInfo = await this.getWindowInfo(message.windowId);
                    sendResponse(windowInfo);
                    break;





                case 'focusWindow':
                    const focusResult = await this.focusWindow(message.windowId);
                    sendResponse(focusResult);
                    break;

                default:
                    console.warn('Unknown message action:', message.action);
                    sendResponse({ success: false, error: 'Unknown action' });
            }
        } catch (error) {
            console.error('Error handling message:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    async handleTabUpdate(tabId, changeInfo, tab) {
        // Only process when page is completely loaded
        if (changeInfo.status !== 'complete' || !tab.url) return;

        // Check if this is a CRM page
        const isCRMPage = this.isCRMPage(tab.url);
        
        if (isCRMPage) {
            // Inject content scripts if needed
            try {
                await chrome.scripting.executeScript({
                    target: { tabId: tabId },
                    files: ['content.js']
                });
            } catch (error) {
                // Content script might already be injected
                console.log('Content script injection skipped:', error.message);
            }

            // Update extension badge
            chrome.action.setBadgeText({
                tabId: tabId,
                text: 'ON'
            });
            chrome.action.setBadgeBackgroundColor({
                tabId: tabId,
                color: '#28a745'
            });
        } else {
            // Clear badge for non-CRM pages
            chrome.action.setBadgeText({
                tabId: tabId,
                text: ''
            });
        }
    }

    handleStorageChange(changes, namespace) {
        if (namespace === 'local') {
            // Notify all CRM tabs about storage changes
            this.notifyAllCRMTabs('storageChanged', changes);
        }
    }

    async getSettings() {
        const result = await chrome.storage.local.get('settings');
        return result.settings || {};
    }

    async updateSettings(newSettings) {
        await chrome.storage.local.set({ settings: newSettings });
        
        // Notify all CRM tabs about settings update
        this.notifyAllCRMTabs('settingsUpdated', newSettings);
    }

    async getScriptState(scriptName) {
        const result = await chrome.storage.local.get(`script_${scriptName}`);
        return result[`script_${scriptName}`] || false;
    }

    async toggleScript(scriptName, enabled) {
        await chrome.storage.local.set({ [`script_${scriptName}`]: enabled });
        
        // Notify all CRM tabs about script state change
        this.notifyAllCRMTabs('scriptToggled', { script: scriptName, enabled });
    }

    async showNotification(title, message, type = 'basic') {
        const settings = await this.getSettings();
        
        if (!settings.enableNotifications) return;

        chrome.notifications.create({
            type: 'basic',
            iconUrl: 'icons/icon48.png',
            title: title,
            message: message
        });
    }

    async logActivity(activity, data) {
        const timestamp = new Date().toISOString();
        const logEntry = {
            timestamp,
            activity,
            data,
            url: data.url || 'unknown'
        };

        // Store activity log (keep last 100 entries)
        const result = await chrome.storage.local.get('activityLog');
        const activityLog = result.activityLog || [];
        
        activityLog.unshift(logEntry);
        if (activityLog.length > 100) {
            activityLog.splice(100);
        }

        await chrome.storage.local.set({ activityLog });
    }

    async checkCRMCompatibility(url) {
        const crmPatterns = [
            /eleadcrm\.com/i,
            /vinsolutions\.com/i,
            /dealersocket\.com/i,
            /cdk\.com/i
        ];

        return crmPatterns.some(pattern => pattern.test(url));
    }

    isCRMPage(url) {
        if (!url) return false;
        
        const crmDomains = [
            'eleadcrm.com',
            'vinsolutions.com',
            'dealersocket.com',
            'cdk.com'
        ];

        return crmDomains.some(domain => url.includes(domain));
    }

    async notifyAllCRMTabs(action, data) {
        try {
            const tabs = await chrome.tabs.query({});
            
            for (const tab of tabs) {
                if (this.isCRMPage(tab.url)) {
                    try {
                        await chrome.tabs.sendMessage(tab.id, {
                            action,
                            data
                        });
                    } catch (error) {
                        // Tab might not have content script loaded
                        console.log(`Could not notify tab ${tab.id}:`, error.message);
                    }
                }
            }
        } catch (error) {
            console.error('Error notifying CRM tabs:', error);
        }
    }

    async handleIframeDetection(data, sender) {
        console.log('Iframe detected:', data);

        // Log iframe detection for debugging
        await this.logActivity('iframe_detected', {
            ...data,
            tabId: sender.tab?.id,
            frameId: sender.frameId,
            url: sender.url
        });
    }

    async handleFrameContextReport(data, sender) {
        console.log('Frame context report:', data);

        // Log frame context for debugging and monitoring
        await this.logActivity('frame_context_report', {
            ...data,
            tabId: sender.tab?.id,
            frameId: sender.frameId,
            url: sender.url
        });

        // Store frame context information for debugging
        try {
            const frameData = await chrome.storage.local.get('frameContexts') || {};
            const frameContexts = frameData.frameContexts || {};

            const tabId = sender.tab?.id;
            if (tabId) {
                if (!frameContexts[tabId]) {
                    frameContexts[tabId] = {};
                }
                frameContexts[tabId][sender.frameId || 'main'] = {
                    ...data,
                    timestamp: new Date().toISOString(),
                    url: sender.url
                };

                await chrome.storage.local.set({ frameContexts });
            }
        } catch (error) {
            console.error('Error storing frame context:', error);
        }
    }

    async createPopupWindow(url, config, urlKey) {
        try {
            const windowConfig = {
                url: url,
                type: 'popup',
                width: config.width,
                height: config.height,
                left: config.left,
                top: config.top
            };

            const window = await chrome.windows.create(windowConfig);

            if (window && window.tabs && window.tabs[0]) {
                // If alwaysOnTop is enabled, set up periodic focusing
                if (config.alwaysOnTop) {
                    this.setupAlwaysOnTop(window.id);
                }

                return {
                    success: true,
                    window: window
                };
            } else {
                return {
                    success: false,
                    error: 'Failed to create popup window'
                };
            }
        } catch (error) {
            console.error('Error creating popup window:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    async getWindowInfo(windowId) {
        try {
            const window = await chrome.windows.get(windowId);
            return {
                success: true,
                window: window
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    async focusWindow(windowId) {
        try {
            await chrome.windows.update(windowId, { focused: true });
            return {
                success: true
            };
        } catch (error) {
            console.error('Error focusing window:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    setupAlwaysOnTop(windowId) {
        // Since Chrome extensions don't support true alwaysOnTop,
        // we'll implement a workaround by periodically checking and focusing the window
        const alwaysOnTopInterval = setInterval(async () => {
            try {
                // Check if window still exists
                const window = await chrome.windows.get(windowId);

                // If window exists and is not focused, focus it
                if (window && !window.focused) {
                    // Get all windows to check if any other window is focused
                    const allWindows = await chrome.windows.getAll();
                    const focusedWindow = allWindows.find(w => w.focused);

                    // Only focus our popup if another window is focused
                    // (avoid interfering with user typing)
                    if (focusedWindow && focusedWindow.id !== windowId) {
                        await chrome.windows.update(windowId, { focused: true });
                    }
                }
            } catch (error) {
                // Window was closed, stop the interval
                clearInterval(alwaysOnTopInterval);
            }
        }, 2000); // Check every 2 seconds

        // Clean up interval when window is removed
        const removeListener = (removedWindowId) => {
            if (removedWindowId === windowId) {
                clearInterval(alwaysOnTopInterval);
                chrome.windows.onRemoved.removeListener(removeListener);
            }
        };
        chrome.windows.onRemoved.addListener(removeListener);
    }





}

// Initialize background service
new TrueBDCBackground();
                overlay.remove();
            };

            saveBtn.addEventListener('click', saveTitle);
            cancelBtn.addEventListener('click', cancelEdit);

            // Handle Enter and Escape keys
            input.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    saveTitle();
                } else if (e.key === 'Escape') {
                    e.preventDefault();
                    cancelEdit();
                }
            });

            // Close on overlay click
            overlay.addEventListener('click', (e) => {
                if (e.target === overlay) {
                    cancelEdit();
                }
            });
        }
    }
}

// Initialize background service
new TrueBDCBackground();
