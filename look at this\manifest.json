{"action": {"default_icon": {"128": "icon/128.png", "16": "icon/16.png", "32": "icon/32.png", "48": "icon/48.png"}}, "background": {"service_worker": "background.js", "type": "module"}, "commands": {"toggle-companion-window": {"description": "__MSG_toggleCompanionWindow__", "suggested_key": {"default": "Alt+C", "mac": "Alt+C"}}}, "content_scripts": [{"js": ["content-scripts/content.js"], "matches": ["<all_urls>"], "run_at": "document_start"}], "content_security_policy": {"extension_pages": "script-src 'self' 'wasm-unsafe-eval'; object-src 'self' 'wasm-unsafe-eval'; worker-src 'self' 'wasm-unsafe-eval';"}, "default_locale": "en", "description": "__MSG_extensionDescription__", "host_permissions": ["<all_urls>"], "icons": {"128": "icon/128.png", "16": "icon/16.png", "32": "icon/32.png", "48": "icon/48.png", "96": "icon/96.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAxUZ3tfBmyXXXW7uhovKnbcNTZ7KiFcMmtqgx2tv7+lX06BD+5z2tcwT6Bi6t9+o0M7fqtt/TTcQFViD2GRQtbyYZn6v1ZXF7nkMv3T77+T6k3lx/I12uzPgIlVMFlvyw1hn9oBO2ZObov9b7jtAnFuBHdoOElmN5M6P6xKXoGhQAJyc67iDojxbJBHlkxz4udmY+mp+gTuuvfYJCuy8S/0PeTe+xbt+kTP6zuwHVAJeAbQIViIADMncEG5RApkDiMsOjBc6wO/2u7oSTbTyJXguQ+GN5NTslOicXwf4fCRpZFFxQS2tSL/JUDbD7VwGrBQGXY5XEOlZZSw+fhtnq9QIDAQAB", "manifest_version": 3, "name": "__MSG_extensionName__", "permissions": ["activeTab", "declarativeNetRequestWithHostAccess", "contextMenus", "storage"], "update_url": "https://clients2.google.com/service/update2/crx", "version": "2.9", "web_accessible_resources": [{"matches": ["<all_urls>"], "resources": ["icon/*", "pip.html", "pip.css"]}]}