<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Welcome to Companion Window</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }

    h1 {
      text-align: center;
      color: #333;
    }

    .section {
      background: #f5f5f5;
      border-radius: 8px;
      padding: 20px;
      margin: 20px 0;
    }

    .section h2 {
      margin-top: 0;
      color: #444;
    }

    .shortcut {
      background: #eee;
      padding: 4px 8px;
      border-radius: 4px;
      font-family: monospace;
    }

    .tip {
      border-left: 4px solid #4CAF50;
      padding-left: 16px;
      margin: 16px 0;
    }

    .footer {
      text-align: center;
      margin-top: 40px;
      color: #666;
    }

    .footer a {
      color: #1a73e8;
      text-decoration: none;
    }

    .footer a:hover {
      text-decoration: underline;
    }

    .status {
      text-align: center;
      padding: 16px;
      margin: 16px 0;
      border-radius: 8px;
      background: #f5f5f5;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 12px;
    }

    .button {
      background: #1a73e8;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: background 0.2s;
    }

    .button:hover {
      background: #1557b0;
    }

    .button:disabled {
      background: #ccc;
      cursor: not-allowed;
    }


  </style>
  <script type="module" crossorigin src="/chunks/welcome-CHoWmKU9.js"></script>
  <link rel="modulepreload" crossorigin href="/chunks/_virtual_wxt-plugins-CDnz5Vh6.js">
</head>
<body>
  <h1>Welcome to Companion Window</h1>

  <div id="status" class="status">
    <div>To activate the extension in your current browser session, you need to refresh your open tabs. This is a one-time setup step.</div>
    <button id="reloadButton" class="button">Refresh Tabs</button>
  </div>

  <div class="section">
    <h2>Getting Started 🚀</h2>
    <p>
      Companion Window lets you transform any webpage into a floating, always-on-top window for seamless multitasking.
      Here's how to use it:
    </p>
    <ul>
      <li>Pin and click on the extension icon to open the Companion Window</li>
      <li>You can also right-click on any webpage and click "Open in Companion Window"</li>
      <li>Use the keyboard shortcut to quickly open the current page in Companion Window</li>
    </ul>
  </div>

  <div class="section">
    <h2>Features 🌟</h2>
    <ul>
      <li>Always-on-top floating window</li>
      <li>Context menu integration</li>
      <li>Minimize to a compact view</li>
      <li>Customizable window size</li>
      <li>Optional Keyboard shortcuts for faster workflow</li>
      <li>Optional Menu button in the top-center corner for additional options(Minimize, Reload)</li>
    </ul>
  </div>

  <div class="section">
    <h2>Tips &amp; Tricks 💡</h2>
    <div class="tip">
      <strong>Tip:</strong> Resize the window by dragging its edges to fit your needs.
    </div>
    <div class="tip">
      <strong>Tip:</strong> When minimized, click anywhere on the window to restore it.
    </div>
    <div class="tip">
      <strong>Tip:</strong> Use the three-dot menu in the top-center corner for additional options(Minimize, Reload).
    </div>
  </div>


  <div class="footer">
    <p style="margin-bottom: 20px; font-weight: 500; color: #333;">
      Made with ❤️ by Anan
    </p>
    <p>
      Having issues or suggestions? <a href="https://github.com/Mohamed3nan/CompanionWindow/issues" target="_blank">Let us know</a>
    </p>
    <p>
      If you find this extension helpful, please consider <a href="#" id="review-link" target="_blank">leaving a review</a>
    </p>
  </div>
</body>
</html> 