/* TrueBDC CRM Automation Suite - Popup Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: transparent; /* Clean transparent background */
    color: #333;
    width: 400px;
    min-height: 520px;
    margin: 0;
    padding: 0;
    overflow: hidden;
}

.container {
    width: 100%;
    height: 100%;
    background: #ffffff;
    border-radius: 18px;
    padding: 0;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    border: 1px solid rgba(0, 0, 0, 0.08);
}

/* Clean border technique for perfect corners */
.container::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 18px;
    background: transparent;
    z-index: 10;
    pointer-events: none;
    box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.06);
}

/* Header */
.header {
    padding: 20px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 18px 18px 0 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo img {
    width: 32px;
    height: 32px;
    border-radius: 10px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    padding: 6px;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.25);
}

.logo h1 {
    font-size: 16px;
    font-weight: 700;
    color: #1e293b;
}

.version {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    padding: 6px 12px;
    border-radius: 14px;
    font-size: 11px;
    font-weight: 600;
    border: 1px solid rgba(102, 126, 234, 0.2);
}

/* Navigation Tabs */
.tabs {
    display: flex;
    background: #f8fafc;
    margin: 16px 20px 0 20px;
    border-radius: 14px;
    padding: 4px;
    border: 1px solid rgba(0, 0, 0, 0.06);
}

.tab-btn {
    flex: 1;
    padding: 10px 16px;
    background: transparent;
    border: none;
    color: #64748b;
    font-size: 13px;
    font-weight: 600;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.tab-btn.active {
    background: white;
    color: #667eea;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.tab-btn:hover:not(.active) {
    background: rgba(0, 0, 0, 0.02);
    color: #475569;
}

/* Content */
.content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Script Groups */
.script-group {
    margin-bottom: 24px;
}

.script-group h3 {
    color: #1e293b;
    font-size: 14px;
    font-weight: 700;
    margin-bottom: 12px;
    padding-left: 4px;
}

.script-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: white;
    padding: 16px;
    border-radius: 14px;
    margin-bottom: 8px;
    border: 1px solid rgba(0, 0, 0, 0.06);
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
}

.script-item:hover {
    border-color: rgba(102, 126, 234, 0.2);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
}

.script-info {
    flex: 1;
}

.script-name {
    display: block;
    color: #1e293b;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 4px;
}

.script-desc {
    color: #64748b;
    font-size: 12px;
    line-height: 1.4;
}

/* Toggle Switch */
.toggle-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.toggle-label span {
    font-weight: 500;
}

.toggle {
    position: relative;
    width: 48px;
    height: 28px;
    margin-left: 16px;
}

.toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #e2e8f0;
    border-radius: 28px;
    transition: 0.3s;
    border: 1px solid rgba(0, 0, 0, 0.06);
}

.slider:before {
    position: absolute;
    content: "";
    height: 22px;
    width: 22px;
    left: 3px;
    bottom: 2px;
    background: white;
    border-radius: 50%;
    transition: 0.3s;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

input:checked + .slider {
    background: #667eea;
    border-color: #667eea;
}

input:checked + .slider:before {
    transform: translateX(20px);
}

/* Settings */
.settings-section {
    margin-bottom: 24px;
}

.settings-section h3 {
    font-size: 14px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 12px;
    padding-bottom: 6px;
    border-bottom: 2px solid #e9ecef;
}

.setting-item {
    margin-bottom: 16px;
}

.setting-item label {
    display: block;
    font-size: 13px;
    font-weight: 500;
    color: #495057;
    margin-bottom: 6px;
}

.setting-item input,
.setting-item select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 13px;
    transition: border-color 0.2s ease;
}

.setting-item input:focus,
.setting-item select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* Buttons */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-right: 8px;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

.settings-actions {
    padding-top: 16px;
    border-top: 1px solid #e9ecef;
}

/* Profiles */
.profile-section h3 {
    font-size: 14px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 12px;
    padding-bottom: 6px;
    border-bottom: 2px solid #e9ecef;
}

.profile-actions {
    margin-bottom: 16px;
}

.profile-list {
    max-height: 300px;
    overflow-y: auto;
}

.profile-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    margin-bottom: 8px;
    background: #f8f9fa;
}

.profile-info {
    flex: 1;
}

.profile-name {
    font-weight: 500;
    color: #212529;
}

.profile-desc {
    font-size: 12px;
    color: #6c757d;
}

/* Footer */
.footer {
    background: #f8f9fa;
    padding: 12px 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.status {
    font-size: 12px;
    color: #28a745;
    font-weight: 500;
}

.links {
    display: flex;
    gap: 12px;
}

.links a {
    font-size: 12px;
    color: #007bff;
    text-decoration: none;
}

.links a:hover {
    text-decoration: underline;
}

/* Scrollbar */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Popup Window Settings Styles */
.dimension-controls {
    display: flex;
    gap: 15px;
    margin-top: 8px;
}

.dimension-group {
    display: flex;
    align-items: center;
    gap: 5px;
    flex: 1;
}

.dimension-group label {
    font-size: 12px;
    color: #666;
    min-width: 45px;
}

.dimension-group input[type="number"] {
    width: 80px;
    padding: 4px 6px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 12px;
}

.dimension-group span {
    font-size: 11px;
    color: #888;
}

.current-dimensions {
    margin-top: 8px;
    padding: 8px;
    background: #f8f9fa;
    border-radius: 4px;
    border-left: 3px solid #007bff;
}

.current-dimensions small {
    color: #666;
    font-size: 11px;
}

.current-dimensions .dimension-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 4px;
}

.current-dimensions .dimension-info:last-child {
    margin-bottom: 0;
}

/* Custom scrollbar */
.content::-webkit-scrollbar {
    width: 6px;
}

.content::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.03);
    border-radius: 3px;
}

.content::-webkit-scrollbar-thumb {
    background: rgba(102, 126, 234, 0.3);
    border-radius: 3px;
}

.content::-webkit-scrollbar-thumb:hover {
    background: rgba(102, 126, 234, 0.5);
}

/* Footer */
.footer {
    padding: 16px 20px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 0 0 18px 18px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid rgba(0, 0, 0, 0.06);
}

.status {
    color: #059669;
    font-size: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 6px;
}

.status::before {
    content: '';
    width: 8px;
    height: 8px;
    background: #10b981;
    border-radius: 50%;
    box-shadow: 0 0 6px rgba(16, 185, 129, 0.4);
}

.links {
    display: flex;
    gap: 20px;
}

.links a {
    color: #64748b;
    text-decoration: none;
    font-size: 12px;
    font-weight: 600;
    transition: all 0.2s ease;
    padding: 4px 8px;
    border-radius: 6px;
}

.links a:hover {
    color: #667eea;
    background: rgba(102, 126, 234, 0.08);
}
