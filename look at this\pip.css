body, html {
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.dropdown {
    position: absolute;
    top: 4px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1001;
    opacity: 1;
    pointer-events: all;
    transition: opacity 0.2s ease;
}

.dropdown.hide {
    opacity: 0;
    pointer-events: none;
}

.dots-button {
    background: rgba(45, 45, 45, 0.4);
    border: none;
    font-size: 14px;
    cursor: pointer;
    padding: 0px 6px;
    color: white;
    border-radius: 10px;
    transition: all 0.2s;
    opacity: 0.4;
    line-height: 1;
    height: 16px;
    display: flex;
    align-items: center;
}

.dots-button:hover {
    background: rgba(45, 45, 45, 0.9);
    opacity: 0.9;
}

.dropdown-menu {
    display: none;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(45, 45, 45, 0.9);
    min-width: 150px;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    margin-top: 10px;
}

.dropdown-menu::before {
    content: '';
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    border-width: 0 10px 10px 10px;
    border-style: solid;
    border-color: transparent transparent rgba(45, 45, 45, 0.9) transparent;
}

.dropdown:hover .dropdown-menu {
    display: block;
}

.menu-item {
    padding: 8px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: rgba(255, 255, 255, 0.9);
    cursor: pointer;
    transition: all 0.2s;
}

.menu-item:hover {
    background: rgba(255, 255, 255, 0.05);
    color: white;
    border-radius: 8px;
}

.menu-item-left {
    display: flex;
    align-items: center;
    gap: 8px;
}

.shortcut {
    color: rgba(255, 255, 255, 0.5);
    font-size: 12px;
}

.icon {
    width: 16px;
    height: 16px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    opacity: 0.9;
}

iframe {
    flex: 1;
    border: none;
    width: 100%;
    height: 100%;
}

.loader {
    border: 5px solid #f3f3f3;
    border-radius: 50%;
    border-top: 5px solid #3498db;
    width: 50px;
    height: 50px;
    animation: spin 1s linear infinite;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.loader-text {
    position: absolute;
    top: 60%;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    color: #3498db;
    font-family: Arial, sans-serif;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

.reload-icon {
    width: 16px;
    height: 16px;
    position: relative;
}

.reload-icon::before {
    content: '';
    position: absolute;
    width: 10px;
    height: 10px;
    border: 2px solid white;
    border-top-color: transparent;
    border-radius: 50%;
    left: 2px;
    top: 2px;
}

.reload-icon::after {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 4px 4px 0;
    border-color: transparent white transparent transparent;
    right: 2px;
    top: 0;
}

.minimized-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.85);
    display: none;
    z-index: 999;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s;
}

.minimized-overlay:hover {
    background: rgba(0, 0, 0, 0.9);
}

.minimized-overlay.show {
    display: flex;
}

.site-info {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 16px;
    background: rgba(50, 50, 50, 0.75);
    border-radius: 8px;
    transition: all 0.2s;
}

.site-info:hover {
    background: rgba(50, 50, 50, 0.9);
}

.site-favicon {
    width: 16px;
    height: 16px;
    object-fit: contain;
}

.site-title {
    color: white;
    font-size: 12px;
    max-width: 150px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}