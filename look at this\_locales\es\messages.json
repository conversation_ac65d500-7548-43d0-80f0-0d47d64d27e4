{"buyMeACoffee": {"message": "☕ Invítame a un café"}, "contextMenu": {"message": "🖱️ Menú Contextual"}, "extensionDescription": {"message": "Transforma cualquier página web en una ventana flotante siempre visible para una multitarea perfecta y productividad mejorada"}, "extensionName": {"message": "Ventana Compañera | Siempre Visible"}, "floatingButton": {"message": "••• <PERSON><PERSON><PERSON>"}, "github": {"message": "🌐 GitHub"}, "inPage": {"message": "En Página"}, "issuesAndSuggestions": {"message": "🤔 Problemas y Sugerencias"}, "keyboardShortcuts": {"message": "⌨️ Atajos de Teclado"}, "leaveAReview": {"message": "🌟 Dejar una reseña"}, "off": {"message": "Desactivado"}, "on": {"message": "Activado"}, "onLink": {"message": "<PERSON>"}, "openInCompanionWindow": {"message": "Abrir en Ventana Compañera"}, "openLinkInCompanionWindow": {"message": "<PERSON><PERSON><PERSON> enlace en Ventana Compañera"}, "options": {"message": "⚙️ Opciones"}, "reloadTabOrBrowser": {"message": "Parece que Companion Window acaba de instalarse o actualizarse. Recargue la pestaña o reinicie su navegador."}, "reportIssue": {"message": "🐛 Reportar Problema"}, "support": {"message": "❤️ Apoyo"}, "toggleCompanionWindow": {"message": "<PERSON><PERSON><PERSON>mp<PERSON>ñ<PERSON>"}, "unsupportedHost": {"message": "Companion Window: no es compatible con este host"}}