{"buyMeACoffee": {"message": "☕ Compre-me um café"}, "contextMenu": {"message": "🖱️ <PERSON><PERSON> de Contexto"}, "extensionDescription": {"message": "Transforme qualquer página da web em uma janela flutuante sempre visível para multitarefas perfeitas e produtividade aprimorada"}, "extensionName": {"message": "Janela Companheira | Sempre Visível"}, "floatingButton": {"message": "••• Botão Flutuante"}, "github": {"message": "🌐 GitHub"}, "inPage": {"message": "<PERSON>"}, "issuesAndSuggestions": {"message": "🤔 Problemas e Sugestões"}, "keyboardShortcuts": {"message": "⌨️ Atalhos de Teclado"}, "leaveAReview": {"message": "🌟 Deixe uma avaliação"}, "off": {"message": "Desativado"}, "on": {"message": "<PERSON><PERSON>do"}, "onLink": {"message": "No Link"}, "openInCompanionWindow": {"message": "Abrir na Janela Companheira"}, "openLinkInCompanionWindow": {"message": "Abrir link na Janela Companheira"}, "options": {"message": "⚙️ Opções"}, "reloadTabOrBrowser": {"message": "Parece que o Companion Window acabou de ser instalado ou atualizado. Recarregue a guia ou reinicie seu navegador."}, "reportIssue": {"message": "🐛 Reportar Problema"}, "support": {"message": "❤️ Suporte"}, "toggleCompanionWindow": {"message": "<PERSON><PERSON><PERSON>"}, "unsupportedHost": {"message": "Companion Window: não é compatível neste host"}}