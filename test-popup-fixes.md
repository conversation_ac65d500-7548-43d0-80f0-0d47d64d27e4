# Tab to Popup Converter - Fix Testing

## Issues Fixed

### 1. Duplicate Popup Prevention
- **Problem**: Ctrl+Alt+9 was sometimes creating multiple popups of the same iframe
- **Solution**: Added check in `convertTabToPopup()` to prevent creating new popup if one already exists
- **Implementation**: 
  - Check `this.popup` and `!this.popup.closed` before creating new popup
  - If popup exists, focus existing window instead of creating new one
  - Show notification that popup already exists
  - Added `focusWindow` action to background script

### 2. Popup Title Renaming
- **Problem**: Popup was showing "Weblead Today" instead of custom/default title
- **Solution**: Improved title setting reliability in popup window
- **Implementation**:
  - Enhanced `createTitleEditingInterface` with multiple title-setting attempts
  - Added retry logic with timeouts (100ms, 500ms, 1000ms)
  - Better fallback to default title "eLeadCRM Weblink"
  - Improved timing for title injection

### 3. Popup Reference Management
- **Problem**: Popup references not properly cleared when window closed
- **Solution**: Added proper cleanup in window removal listeners
- **Implementation**:
  - Clear `this.popup` reference in both `monitorPopupWindow` and `setupPopupHandlers`
  - Ensures duplicate prevention works correctly after popup is closed

## Testing Steps

1. **Test Duplicate Prevention**:
   - Navigate to eLeadCRM weblink iframe
   - Press Ctrl+Alt+9 to create popup
   - Press Ctrl+Alt+9 again - should focus existing popup, not create new one
   - Should see green notification: "Popup already exists - focusing existing window"

2. **Test Title Setting**:
   - Create popup with Ctrl+Alt+9
   - Popup title should be "eLeadCRM Weblink" (default) or saved custom title
   - Should NOT show "Weblead Today"
   - Click blue title bar to edit title
   - Save custom title and verify it persists

3. **Test Reference Cleanup**:
   - Create popup with Ctrl+Alt+9
   - Close popup window
   - Press Ctrl+Alt+9 again - should create new popup (not try to focus closed one)

## Code Changes Made

### scripts/tab-to-popup.js
- Added duplicate prevention check in `convertTabToPopup()`
- Added `showExistingPopupNotification()` method
- Improved popup reference cleanup in window removal listeners

### background.js
- Added `focusWindow()` method
- Added 'focusWindow' message handler
- Enhanced `createTitleEditingInterface()` with retry logic
- Improved timing for title injection

## Expected Behavior
- No more duplicate popups
- Proper custom titles or default "eLeadCRM Weblink" title
- Smooth focus behavior when trying to create duplicate
- Proper cleanup allowing new popups after closing
