// TrueBDC CRM Automation Suite - Popup Title Changer
// Based on user's working Tampermonkey script approach

class PopupTitleChanger {
    constructor(settings = {}) {
        this.settings = settings;
        this.isActive = false;
        this.customTitle = '';
        this.observer = null;
        
        this.init();
    }

    async init() {
        try {
            // Only run in popup windows (not in main CRM page)
            if (!this.isPopupWindow()) {
                return;
            }

            await this.loadCustomTitle();
            this.setupTitleChanger();
            this.isActive = true;
            
            TrueBDCUtils.log('Popup Title Changer initialized', {
                customTitle: this.customTitle,
                url: window.location.href
            });
        } catch (error) {
            TrueBDCUtils.error('Error initializing Popup Title Changer', error);
        }
    }

    isPopupWindow() {
        // Check if this is a popup window by checking window properties
        try {
            // Popup windows typically have different characteristics
            const isPopup = window.opener !== null || 
                           window.name === 'popup' ||
                           window.location !== window.parent.location ||
                           (window.outerWidth < screen.width * 0.8 && window.outerHeight < screen.height * 0.8);
            
            return isPopup;
        } catch (error) {
            // If we can't determine, assume it's not a popup
            return false;
        }
    }

    async loadCustomTitle() {
        try {
            // Load dealership name from settings
            const result = await chrome.storage.local.get('settings');
            if (result.settings && result.settings.dealershipName && result.settings.dealershipName.trim()) {
                this.customTitle = result.settings.dealershipName.trim();
                TrueBDCUtils.log('Loaded custom title from settings', {
                    customTitle: this.customTitle
                });
                return;
            }

            // Fallback to legacy storage
            const legacyResult = await chrome.storage.local.get('customDealershipName');
            if (legacyResult.customDealershipName && legacyResult.customDealershipName.trim()) {
                this.customTitle = legacyResult.customDealershipName.trim();
                TrueBDCUtils.log('Loaded custom title from legacy storage', {
                    customTitle: this.customTitle
                });
                return;
            }

            // Default fallback
            this.customTitle = 'Downtown Auto Center';
            TrueBDCUtils.log('Using default title', {
                customTitle: this.customTitle
            });
        } catch (error) {
            TrueBDCUtils.error('Error loading custom title', error);
            this.customTitle = 'Downtown Auto Center';
        }
    }

    setupTitleChanger() {
        // EXACT COPY of the working Tampermonkey script approach
        const customTitle = this.customTitle;
        
        // Set initial title
        document.title = customTitle;
        TrueBDCUtils.log('Set initial popup title', { title: customTitle });

        // Function to update title if it changes
        this.observer = new MutationObserver(mutations => {
            if (document.title !== customTitle) {
                document.title = customTitle;
                TrueBDCUtils.log('Popup title restored', { 
                    from: document.title, 
                    to: customTitle 
                });
            }
        });

        // Start observing the document head for changes to the title
        const titleElement = document.querySelector('title');
        if (titleElement) {
            this.observer.observe(titleElement, { childList: true });
            TrueBDCUtils.log('Title observer started for popup window');
        } else {
            TrueBDCUtils.log('No title element found, creating one');
            // Create title element if it doesn't exist
            const head = document.head || document.getElementsByTagName('head')[0];
            const title = document.createElement('title');
            title.textContent = customTitle;
            head.appendChild(title);
        }

        // Also set title periodically as backup (every 2 seconds)
        this.titleInterval = setInterval(() => {
            if (document.title !== customTitle) {
                document.title = customTitle;
            }
        }, 2000);
    }

    async updateSettings(newSettings) {
        if (newSettings.dealershipName && newSettings.dealershipName.trim()) {
            const oldTitle = this.customTitle;
            this.customTitle = newSettings.dealershipName.trim();
            
            if (oldTitle !== this.customTitle && this.isActive) {
                // Update title immediately
                document.title = this.customTitle;
                TrueBDCUtils.log('Popup title updated from settings', {
                    from: oldTitle,
                    to: this.customTitle
                });
            }
        }
    }

    destroy() {
        try {
            if (this.observer) {
                this.observer.disconnect();
                this.observer = null;
            }
            
            if (this.titleInterval) {
                clearInterval(this.titleInterval);
                this.titleInterval = null;
            }
            
            this.isActive = false;
            TrueBDCUtils.log('Popup Title Changer destroyed');
        } catch (error) {
            TrueBDCUtils.error('Error destroying Popup Title Changer', error);
        }
    }

    // Static method to check if script should be available
    static isAvailable() {
        // Available on all supported CRM pages
        const url = window.location.href;
        return /eleadcrm\.com|vinsolutions\.com/i.test(url);
    }
}

// Auto-initialize if we're in a popup window
if (typeof window !== 'undefined' && window.location) {
    // Small delay to ensure DOM is ready
    setTimeout(() => {
        if (PopupTitleChanger.isAvailable()) {
            window.trueBDCPopupTitleChanger = new PopupTitleChanger();
        }
    }, 100);
}
