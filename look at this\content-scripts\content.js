var content=function(){"use strict";var Ee=Object.defineProperty;var be=(p,M,P)=>M in p?Ee(p,M,{enumerable:!0,configurable:!0,writable:!0,value:P}):p[M]=P;var k=(p,M,P)=>be(p,typeof M!="symbol"?M+"":M,P);var Q,J,X,Z;var p=Object.prototype.hasOwnProperty;function M(i,e){var t,r;if(i===e)return!0;if(i&&e&&(t=i.constructor)===e.constructor){if(t===Date)return i.getTime()===e.getTime();if(t===RegExp)return i.toString()===e.toString();if(t===Array){if((r=i.length)===e.length)for(;r--&&M(i[r],e[r]););return r===-1}if(!t||typeof i=="object"){r=0;for(t in i)if(p.call(i,t)&&++r&&!p.call(e,t)||!(t in e)||!M(i[t],e[t]))return!1;return Object.keys(e).length===r}}return i!==i&&e!==e}const P=new Error("request for lock canceled");var te=function(i,e,t,r){function h(c){return c instanceof t?c:new t(function(u){u(c)})}return new(t||(t=Promise))(function(c,u){function m(w){try{I(r.next(w))}catch(y){u(y)}}function _(w){try{I(r.throw(w))}catch(y){u(y)}}function I(w){w.done?c(w.value):h(w.value).then(m,_)}I((r=r.apply(i,e||[])).next())})};class ne{constructor(e,t=P){this._value=e,this._cancelError=t,this._queue=[],this._weightedWaiters=[]}acquire(e=1,t=0){if(e<=0)throw new Error(`invalid weight ${e}: must be positive`);return new Promise((r,h)=>{const c={resolve:r,reject:h,weight:e,priority:t},u=G(this._queue,m=>t<=m.priority);u===-1&&e<=this._value?this._dispatchItem(c):this._queue.splice(u+1,0,c)})}runExclusive(e){return te(this,arguments,void 0,function*(t,r=1,h=0){const[c,u]=yield this.acquire(r,h);try{return yield t(c)}finally{u()}})}waitForUnlock(e=1,t=0){if(e<=0)throw new Error(`invalid weight ${e}: must be positive`);return this._couldLockImmediately(e,t)?Promise.resolve():new Promise(r=>{this._weightedWaiters[e-1]||(this._weightedWaiters[e-1]=[]),re(this._weightedWaiters[e-1],{resolve:r,priority:t})})}isLocked(){return this._value<=0}getValue(){return this._value}setValue(e){this._value=e,this._dispatchQueue()}release(e=1){if(e<=0)throw new Error(`invalid weight ${e}: must be positive`);this._value+=e,this._dispatchQueue()}cancel(){this._queue.forEach(e=>e.reject(this._cancelError)),this._queue=[]}_dispatchQueue(){for(this._drainUnlockWaiters();this._queue.length>0&&this._queue[0].weight<=this._value;)this._dispatchItem(this._queue.shift()),this._drainUnlockWaiters()}_dispatchItem(e){const t=this._value;this._value-=e.weight,e.resolve([t,this._newReleaser(e.weight)])}_newReleaser(e){let t=!1;return()=>{t||(t=!0,this.release(e))}}_drainUnlockWaiters(){if(this._queue.length===0)for(let e=this._value;e>0;e--){const t=this._weightedWaiters[e-1];t&&(t.forEach(r=>r.resolve()),this._weightedWaiters[e-1]=[])}else{const e=this._queue[0].priority;for(let t=this._value;t>0;t--){const r=this._weightedWaiters[t-1];if(!r)continue;const h=r.findIndex(c=>c.priority<=e);(h===-1?r:r.splice(0,h)).forEach(c=>c.resolve())}}}_couldLockImmediately(e,t){return(this._queue.length===0||this._queue[0].priority<t)&&e<=this._value}}function re(i,e){const t=G(i,r=>e.priority<=r.priority);i.splice(t+1,0,e)}function G(i,e){for(let t=i.length-1;t>=0;t--)if(e(i[t]))return t;return-1}var ie=function(i,e,t,r){function h(c){return c instanceof t?c:new t(function(u){u(c)})}return new(t||(t=Promise))(function(c,u){function m(w){try{I(r.next(w))}catch(y){u(y)}}function _(w){try{I(r.throw(w))}catch(y){u(y)}}function I(w){w.done?c(w.value):h(w.value).then(m,_)}I((r=r.apply(i,e||[])).next())})};class se{constructor(e){this._semaphore=new ne(1,e)}acquire(){return ie(this,arguments,void 0,function*(e=0){const[,t]=yield this._semaphore.acquire(1,e);return t})}runExclusive(e,t=0){return this._semaphore.runExclusive(()=>e(),1,t)}isLocked(){return this._semaphore.isLocked()}waitForUnlock(e=0){return this._semaphore.waitForUnlock(1,e)}release(){this._semaphore.isLocked()&&this._semaphore.release()}cancel(){return this._semaphore.cancel()}}const C=((J=(Q=globalThis.browser)==null?void 0:Q.runtime)==null?void 0:J.id)==null?globalThis.chrome:globalThis.browser,Y=ae();function ae(){const i={local:R("local"),session:R("session"),sync:R("sync"),managed:R("managed")},e=a=>{const s=i[a];if(s==null){const n=Object.keys(i).join(", ");throw Error(`Invalid area "${a}". Options: ${n}`)}return s},t=a=>{const s=a.indexOf(":"),n=a.substring(0,s),o=a.substring(s+1);if(o==null)throw Error(`Storage key should be in the form of "area:key", but received "${a}"`);return{driverArea:n,driverKey:o,driver:e(n)}},r=a=>a+"$",h=(a,s)=>{const n={...a};return Object.entries(s).forEach(([o,l])=>{l==null?delete n[o]:n[o]=l}),n},c=(a,s)=>a??s??null,u=a=>typeof a=="object"&&!Array.isArray(a)?a:{},m=async(a,s,n)=>{const o=await a.getItem(s);return c(o,(n==null?void 0:n.fallback)??(n==null?void 0:n.defaultValue))},_=async(a,s)=>{const n=r(s),o=await a.getItem(n);return u(o)},I=async(a,s,n)=>{await a.setItem(s,n??null)},w=async(a,s,n)=>{const o=r(s),l=u(await a.getItem(o));await a.setItem(o,h(l,n))},y=async(a,s,n)=>{if(await a.removeItem(s),n!=null&&n.removeMeta){const o=r(s);await a.removeItem(o)}},x=async(a,s,n)=>{const o=r(s);if(n==null)await a.removeItem(o);else{const l=u(await a.getItem(o));[n].flat().forEach(d=>delete l[d]),await a.setItem(o,l)}},T=(a,s,n)=>a.watch(s,n);return{getItem:async(a,s)=>{const{driver:n,driverKey:o}=t(a);return await m(n,o,s)},getItems:async a=>{const s=new Map,n=new Map,o=[];a.forEach(d=>{let f,g;typeof d=="string"?f=d:"getValue"in d?(f=d.key,g={fallback:d.fallback}):(f=d.key,g=d.options),o.push(f);const{driverArea:L,driverKey:E}=t(f),b=s.get(L)??[];s.set(L,b.concat(E)),n.set(f,g)});const l=new Map;return await Promise.all(Array.from(s.entries()).map(async([d,f])=>{(await i[d].getItems(f)).forEach(L=>{const E=`${d}:${L.key}`,b=n.get(E),v=c(L.value,(b==null?void 0:b.fallback)??(b==null?void 0:b.defaultValue));l.set(E,v)})})),o.map(d=>({key:d,value:l.get(d)}))},getMeta:async a=>{const{driver:s,driverKey:n}=t(a);return await _(s,n)},getMetas:async a=>{const s=a.map(l=>{const d=typeof l=="string"?l:l.key,{driverArea:f,driverKey:g}=t(d);return{key:d,driverArea:f,driverKey:g,driverMetaKey:r(g)}}),n=s.reduce((l,d)=>{var f;return l[f=d.driverArea]??(l[f]=[]),l[d.driverArea].push(d),l},{}),o={};return await Promise.all(Object.entries(n).map(async([l,d])=>{const f=await C.storage[l].get(d.map(g=>g.driverMetaKey));d.forEach(g=>{o[g.key]=f[g.driverMetaKey]??{}})})),s.map(l=>({key:l.key,meta:o[l.key]}))},setItem:async(a,s)=>{const{driver:n,driverKey:o}=t(a);await I(n,o,s)},setItems:async a=>{const s={};a.forEach(n=>{const{driverArea:o,driverKey:l}=t("key"in n?n.key:n.item.key);s[o]??(s[o]=[]),s[o].push({key:l,value:n.value})}),await Promise.all(Object.entries(s).map(async([n,o])=>{await e(n).setItems(o)}))},setMeta:async(a,s)=>{const{driver:n,driverKey:o}=t(a);await w(n,o,s)},setMetas:async a=>{const s={};a.forEach(n=>{const{driverArea:o,driverKey:l}=t("key"in n?n.key:n.item.key);s[o]??(s[o]=[]),s[o].push({key:l,properties:n.meta})}),await Promise.all(Object.entries(s).map(async([n,o])=>{const l=e(n),d=o.map(({key:E})=>r(E));console.log(n,d);const f=await l.getItems(d),g=Object.fromEntries(f.map(({key:E,value:b})=>[E,u(b)])),L=o.map(({key:E,properties:b})=>{const v=r(E);return{key:v,value:h(g[v]??{},b)}});await l.setItems(L)}))},removeItem:async(a,s)=>{const{driver:n,driverKey:o}=t(a);await y(n,o,s)},removeItems:async a=>{const s={};a.forEach(n=>{let o,l;typeof n=="string"?o=n:"getValue"in n?o=n.key:"item"in n?(o=n.item.key,l=n.options):(o=n.key,l=n.options);const{driverArea:d,driverKey:f}=t(o);s[d]??(s[d]=[]),s[d].push(f),l!=null&&l.removeMeta&&s[d].push(r(f))}),await Promise.all(Object.entries(s).map(async([n,o])=>{await e(n).removeItems(o)}))},clear:async a=>{await e(a).clear()},removeMeta:async(a,s)=>{const{driver:n,driverKey:o}=t(a);await x(n,o,s)},snapshot:async(a,s)=>{var l;const o=await e(a).snapshot();return(l=s==null?void 0:s.excludeKeys)==null||l.forEach(d=>{delete o[d],delete o[r(d)]}),o},restoreSnapshot:async(a,s)=>{await e(a).restoreSnapshot(s)},watch:(a,s)=>{const{driver:n,driverKey:o}=t(a);return T(n,o,s)},unwatch(){Object.values(i).forEach(a=>{a.unwatch()})},defineItem:(a,s)=>{const{driver:n,driverKey:o}=t(a),{version:l=1,migrations:d={}}=s??{};if(l<1)throw Error("Storage item version cannot be less than 1. Initial versions should be set to 1, not 0.");const f=async()=>{var ee;const v=r(o),[{value:$},{value:V}]=await n.getItems([o,v]);if($==null)return;const q=(V==null?void 0:V.v)??1;if(q>l)throw Error(`Version downgrade detected (v${q} -> v${l}) for "${a}"`);if(q===l)return;console.debug(`[@wxt-dev/storage] Running storage migration for ${a}: v${q} -> v${l}`);const Ie=Array.from({length:l-q},(H,D)=>q+D+1);let K=$;for(const H of Ie)try{K=await((ee=d==null?void 0:d[H])==null?void 0:ee.call(d,K))??K}catch(D){throw new oe(a,H,{cause:D})}await n.setItems([{key:o,value:K},{key:v,value:{...V,v:l}}]),console.debug(`[@wxt-dev/storage] Storage migration completed for ${a} v${l}`,{migratedValue:K})},g=(s==null?void 0:s.migrations)==null?Promise.resolve():f().catch(v=>{console.error(`[@wxt-dev/storage] Migration failed for ${a}`,v)}),L=new se,E=()=>(s==null?void 0:s.fallback)??(s==null?void 0:s.defaultValue)??null,b=()=>L.runExclusive(async()=>{const v=await n.getItem(o);if(v!=null||(s==null?void 0:s.init)==null)return v;const $=await s.init();return await n.setItem(o,$),$});return g.then(b),{key:a,get defaultValue(){return E()},get fallback(){return E()},getValue:async()=>(await g,s!=null&&s.init?await b():await m(n,o,s)),getMeta:async()=>(await g,await _(n,o)),setValue:async v=>(await g,await I(n,o,v)),setMeta:async v=>(await g,await w(n,o,v)),removeValue:async v=>(await g,await y(n,o,v)),removeMeta:async v=>(await g,await x(n,o,v)),watch:v=>T(n,o,($,V)=>v($??E(),V??E())),migrate:f}}}}function R(i){const e=()=>{if(C.runtime==null)throw Error(["'wxt/storage' must be loaded in a web extension environment",`
 - If thrown during a build, see https://github.com/wxt-dev/wxt/issues/371`,` - If thrown during tests, mock 'wxt/browser' correctly. See https://wxt.dev/guide/go-further/testing.html
`].join(`
`));if(C.storage==null)throw Error("You must add the 'storage' permission to your manifest to use 'wxt/storage'");const r=C.storage[i];if(r==null)throw Error(`"browser.storage.${i}" is undefined`);return r},t=new Set;return{getItem:async r=>(await e().get(r))[r],getItems:async r=>{const h=await e().get(r);return r.map(c=>({key:c,value:h[c]??null}))},setItem:async(r,h)=>{h==null?await e().remove(r):await e().set({[r]:h})},setItems:async r=>{const h=r.reduce((c,{key:u,value:m})=>(c[u]=m,c),{});await e().set(h)},removeItem:async r=>{await e().remove(r)},removeItems:async r=>{await e().remove(r)},clear:async()=>{await e().clear()},snapshot:async()=>await e().get(),restoreSnapshot:async r=>{await e().set(r)},watch(r,h){const c=u=>{const m=u[r];m!=null&&(M(m.newValue,m.oldValue)||h(m.newValue??null,m.oldValue??null))};return e().onChanged.addListener(c),t.add(c),()=>{e().onChanged.removeListener(c),t.delete(c)}},unwatch(){t.forEach(r=>{e().onChanged.removeListener(r)}),t.clear()}}}class oe extends Error{constructor(e,t,r){super(`v${t} migration failed for "${e}"`,r),this.key=e,this.version=t}}function _e(i){return i}const ce=Y.defineItem("local:extLastUrl"),le=Y.defineItem("local:extFloatingButtonEnabled",{defaultValue:!0}),S={isMinimized:!1,originalWidth:400,originalHeight:600};function ue(i){const e=i.document.createElement("div"),t=i.document.createElement("div");t.className="loader";const r=i.document.createElement("div");return r.className="loader-text",e.appendChild(t),e.appendChild(r),i.document.body.appendChild(e),e}function de(i){const e=i.document.querySelector(".dropdown"),t=i.document.querySelector(".dots-button"),r=i.document.querySelector(".menu-item.minimize"),h=i.document.querySelector(".menu-item.reload"),c=i.document.querySelector(".minimized-overlay"),u=i.document.querySelector(".site-favicon"),m=i.document.querySelector(".site-title");if(!e||!t||!r||!h||!c||!u||!m){console.error("Required elements not found");return}const _=()=>{var s;const a=i.document.getElementById("companionWindow");if(a)try{const n=a.contentDocument||((s=a.contentWindow)==null?void 0:s.document);if(!a.src||a.src==="about:blank"){u.src=chrome.runtime.getURL("/icon/48.png"),m.textContent="";return}const o=new URL(a.src),l=(n==null?void 0:n.querySelector('link[rel="icon"]'))||(n==null?void 0:n.querySelector('link[rel="shortcut icon"]'));l&&"href"in l&&l.href?u.src=l.href:u.src=`https://www.google.com/s2/favicons?domain=${o.hostname}&sz=32`,m.textContent=(n==null?void 0:n.title)||o.hostname||""}catch(n){console.error("Error accessing iframe content:",n);try{if(!a.src||a.src==="about:blank"){u.src=chrome.runtime.getURL("/icon/48.png"),m.textContent="";return}const o=new URL(a.src);u.src=`https://www.google.com/s2/favicons?domain=${o.hostname}&sz=32`,m.textContent=o.hostname||""}catch(o){console.error("Error parsing URL:",o),u.src=chrome.runtime.getURL("/icon/48.png"),m.textContent=""}}},I=250,w=75,y=()=>{i.innerWidth<I&&i.innerHeight<w&&(S.isMinimized=!0,c.classList.add("show"),e.classList.add("hide"),_())},x=i.document.getElementById("companionWindow");x&&x.addEventListener("load",()=>{y(),_()});const T=()=>{S.isMinimized&&(i.resizeTo(S.originalWidth,S.originalHeight),S.isMinimized=!1,c.classList.remove("show"),e.classList.remove("hide"))};c.addEventListener("click",()=>{T()}),r.addEventListener("click",a=>{a.stopPropagation(),S.isMinimized?T():(S.originalWidth=i.innerWidth,S.originalHeight=i.innerHeight,i.resizeTo(I-50,w-5),S.isMinimized=!0,c.classList.add("show"),e.classList.add("hide"),_())});let W;i.addEventListener("resize",()=>{clearTimeout(W),W=setTimeout(()=>{i.innerWidth<I&&i.innerHeight<w?S.isMinimized||(S.isMinimized=!0,c.classList.add("show"),e.classList.add("hide"),_()):(S.isMinimized||c.classList.contains("show"))&&(S.isMinimized=!1,c.classList.remove("show"),e.classList.remove("hide"))},100)}),h.addEventListener("click",a=>{a.stopPropagation();const s=i.document.getElementById("dropdown-toggle");s&&(s.checked=!1);const n=i.document.getElementById("companionWindow");n&&(n.src=n.src)})}async function he(i){try{const e=await le.getValue(),t=i.document.querySelector(".dropdown");t&&(e||t.classList.add("hide"))}catch(e){console.error("Error getting floating button state:",e)}}async function me(){var i;try{const e=await((i=window.documentPictureInPicture)==null?void 0:i.requestWindow({width:400,height:600}));if(!e){console.error("Could not create Picture-in-Picture window");return}e.chrome=chrome;const r=await(await fetch(chrome.runtime.getURL("/pip.css"))).text(),c=await(await fetch(chrome.runtime.getURL("/pip.html"))).text();e.document.write(`
      <style>${r}</style>
      ${c}
    `),e.document.close(),de(e),await he(e);const u=ue(e),_=await new Promise(w=>{const y=e.document.getElementById("companionWindow");if(y){w(y);return}const x=new MutationObserver(()=>{const T=e.document.getElementById("companionWindow");T&&(x.disconnect(),w(T))});x.observe(e.document.body,{childList:!0,subtree:!0})}),I=await we();I?(_.src=I,_.addEventListener("load",()=>{u&&u.remove()})):u&&u.remove(),e.addEventListener("pagehide",w=>{const y=window.documentPictureInPicture;y!=null&&y.window&&(y.window.close(),chrome.runtime.sendMessage({action:"cleanupRules"}))})}catch(e){console.error("Failed to enter Picture-in-Picture mode:",e)}}async function we(){try{return await ce.getValue()||null}catch(i){return console.error("Error getting stored URL:",i),null}}const fe={matches:["<all_urls>"],runAt:"document_start",main(i){chrome.runtime.onMessage.addListener((e,t,r)=>{var h;if(e.action==="checkPiPWindow"){const c=((h=window.documentPictureInPicture)==null?void 0:h.window)!=null;r({hasPiPWindow:c})}else if(e.action==="closePiP"){const c=window.documentPictureInPicture;c!=null&&c.window&&(c.window.close(),chrome.runtime.sendMessage({action:"cleanupRules"})),r({success:!0})}else if(e.action==="openPiP")me(),r({received:!0});else if(e.action==="toggleFloatingButton"){const c=window.documentPictureInPicture;if(c!=null&&c.window){const u=c.window.document.querySelector(".dropdown");u&&(e.state?u.classList.remove("hide"):u.classList.add("hide"))}r({received:!0})}return!0})}},U=(Z=(X=globalThis.browser)==null?void 0:X.runtime)!=null&&Z.id?globalThis.browser:globalThis.chrome;function z(i,...e){}const ge={debug:(...i)=>z(console.debug,...i),log:(...i)=>z(console.log,...i),warn:(...i)=>z(console.warn,...i),error:(...i)=>z(console.error,...i)},A=class A extends Event{constructor(e,t){super(A.EVENT_NAME,{}),this.newUrl=e,this.oldUrl=t}};k(A,"EVENT_NAME",j("wxt:locationchange"));let O=A;function j(i){var e;return`${(e=U==null?void 0:U.runtime)==null?void 0:e.id}:content:${i}`}function ve(i){let e,t;return{run(){e==null&&(t=new URL(location.href),e=i.setInterval(()=>{let r=new URL(location.href);r.href!==t.href&&(window.dispatchEvent(new O(r,t)),t=r)},1e3))}}}const F=class F{constructor(e,t){k(this,"isTopFrame",window.self===window.top);k(this,"abortController");k(this,"locationWatcher",ve(this));k(this,"receivedMessageIds",new Set);this.contentScriptName=e,this.options=t,this.abortController=new AbortController,this.isTopFrame?(this.listenForNewerScripts({ignoreFirstEvent:!0}),this.stopOldScripts()):this.listenForNewerScripts()}get signal(){return this.abortController.signal}abort(e){return this.abortController.abort(e)}get isInvalid(){return U.runtime.id==null&&this.notifyInvalidated(),this.signal.aborted}get isValid(){return!this.isInvalid}onInvalidated(e){return this.signal.addEventListener("abort",e),()=>this.signal.removeEventListener("abort",e)}block(){return new Promise(()=>{})}setInterval(e,t){const r=setInterval(()=>{this.isValid&&e()},t);return this.onInvalidated(()=>clearInterval(r)),r}setTimeout(e,t){const r=setTimeout(()=>{this.isValid&&e()},t);return this.onInvalidated(()=>clearTimeout(r)),r}requestAnimationFrame(e){const t=requestAnimationFrame((...r)=>{this.isValid&&e(...r)});return this.onInvalidated(()=>cancelAnimationFrame(t)),t}requestIdleCallback(e,t){const r=requestIdleCallback((...h)=>{this.signal.aborted||e(...h)},t);return this.onInvalidated(()=>cancelIdleCallback(r)),r}addEventListener(e,t,r,h){var c;t==="wxt:locationchange"&&this.isValid&&this.locationWatcher.run(),(c=e.addEventListener)==null||c.call(e,t.startsWith("wxt:")?j(t):t,r,{...h,signal:this.signal})}notifyInvalidated(){this.abort("Content script context invalidated"),ge.debug(`Content script "${this.contentScriptName}" context invalidated`)}stopOldScripts(){window.postMessage({type:F.SCRIPT_STARTED_MESSAGE_TYPE,contentScriptName:this.contentScriptName,messageId:Math.random().toString(36).slice(2)},"*")}verifyScriptStartedEvent(e){var c,u,m;const t=((c=e.data)==null?void 0:c.type)===F.SCRIPT_STARTED_MESSAGE_TYPE,r=((u=e.data)==null?void 0:u.contentScriptName)===this.contentScriptName,h=!this.receivedMessageIds.has((m=e.data)==null?void 0:m.messageId);return t&&r&&h}listenForNewerScripts(e){let t=!0;const r=h=>{if(this.verifyScriptStartedEvent(h)){this.receivedMessageIds.add(h.data.messageId);const c=t;if(t=!1,c&&(e!=null&&e.ignoreFirstEvent))return;this.notifyInvalidated()}};addEventListener("message",r),this.onInvalidated(()=>removeEventListener("message",r))}};k(F,"SCRIPT_STARTED_MESSAGE_TYPE",j("wxt:content-script-started"));let B=F;function Me(){}function N(i,...e){}const ye={debug:(...i)=>N(console.debug,...i),log:(...i)=>N(console.log,...i),warn:(...i)=>N(console.warn,...i),error:(...i)=>N(console.error,...i)};return(async()=>{try{const{main:i,...e}=fe,t=new B("content",e);return await i(t)}catch(i){throw ye.error('The content script "content" crashed on startup!',i),i}})()}();
content;
