// TrueBDC CRM Automation Suite - Auto Close Release Notes
// Automatically closes the eLeadCRM release notes popup window

class AutoCloseReleaseNotes {
    constructor(settings = {}) {
        this.settings = settings;
        this.isActive = false;
        
        console.log('[AutoCloseReleaseNotes] Constructor called');
        console.log('[AutoCloseReleaseNotes] Current URL:', window.location.href);
        
        this.init();
    }

    async init() {
        try {
            console.log('[AutoCloseReleaseNotes] Init started');
            
            // Check if we're on the release notes popup page
            if (this.isReleaseNotesPage()) {
                console.log('[AutoCloseReleaseNotes] Release notes page detected, closing window');
                this.closeReleaseNotesWindow();
                this.isActive = true;
                
                TrueBDCUtils.log('Auto Close Release Notes activated', {
                    url: window.location.href
                });
            } else {
                console.log('[AutoCloseReleaseNotes] Not a release notes page, skipping');
                console.log('[AutoCloseReleaseNotes] Current URL:', window.location.href);
                console.log('[AutoCloseReleaseNotes] Required URL pattern: releasenotesviewall.aspx');
            }
        } catch (error) {
            console.error('[AutoCloseReleaseNotes] Error in init:', error);
            TrueBDCUtils.error('Error initializing Auto Close Release Notes', error);
        }
    }

    isReleaseNotesPage() {
        // Check if the URL matches the release notes popup (EXACT COPY of Tampermonkey logic)
        const url = window.location.href;
        const isReleaseNotes = url.includes('releasenotesviewall.aspx');
        
        console.log('[AutoCloseReleaseNotes] URL check:', {
            currentUrl: url,
            isReleaseNotes: isReleaseNotes,
            requiredPattern: 'releasenotesviewall.aspx'
        });
        
        return isReleaseNotes;
    }

    closeReleaseNotesWindow() {
        try {
            console.log('[AutoCloseReleaseNotes] Attempting to close window');
            
            // EXACT COPY of Tampermonkey logic
            window.close();
            
            console.log('[AutoCloseReleaseNotes] Window.close() called');
            
            // Log activity
            TrueBDCUtils.logActivity('auto_close_release_notes', {
                url: window.location.href,
                timestamp: new Date().toISOString()
            });

            // Show notification (brief since window is closing)
            this.showCloseNotification();
            
        } catch (error) {
            console.error('[AutoCloseReleaseNotes] Error closing window:', error);
            TrueBDCUtils.error('Error closing release notes window', error);
        }
    }

    showCloseNotification() {
        // Brief notification since window is closing anyway
        const notification = TrueBDCUtils.createElement('div', {
            id: 'truebdc-close-notification'
        }, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: 'linear-gradient(135deg, #28a745, #1e7e34)',
            color: 'white',
            padding: '12px 16px',
            borderRadius: '6px',
            fontSize: '13px',
            fontWeight: '500',
            zIndex: '999999',
            boxShadow: '0 3px 10px rgba(40, 167, 69, 0.3)',
            opacity: '0',
            transition: 'opacity 0.3s ease'
        });

        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 8px;">
                <span>✅</span>
                <span>Auto-closing release notes popup</span>
            </div>
        `;

        document.body.appendChild(notification);

        // Fade in quickly
        setTimeout(() => {
            notification.style.opacity = '1';
        }, 10);

        // Window will close soon, so notification doesn't need to fade out
    }

    destroy() {
        this.isActive = false;
        console.log('[AutoCloseReleaseNotes] Auto Close Release Notes destroyed');
        TrueBDCUtils.log('Auto Close Release Notes destroyed');
    }

    // Static method to check if script should be available
    static isAvailable() {
        const url = window.location.href;
        return url.includes('eleadcrm.com');
    }
}

// Script loaded - will be initialized by content script when enabled
console.log('[AutoCloseReleaseNotes] Script class loaded, waiting for content script control');
