import{i as c}from"./_virtual_wxt-plugins-CDnz5Vh6.js";(function(){const o=document.createElement("link").relList;if(o&&o.supports&&o.supports("modulepreload"))return;for(const e of document.querySelectorAll('link[rel="modulepreload"]'))i(e);new MutationObserver(e=>{for(const t of e)if(t.type==="childList")for(const s of t.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&i(s)}).observe(document,{childList:!0,subtree:!0});function n(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),e.crossOrigin==="use-credentials"?t.credentials="include":e.crossOrigin==="anonymous"?t.credentials="omit":t.credentials="same-origin",t}function i(e){if(e.ep)return;e.ep=!0;const t=n(e);fetch(e.href,t)}})();try{c()}catch(r){console.error("[wxt] Failed to initialize plugins",r)}document.addEventListener("DOMContentLoaded",function(){const r=document.getElementById("reloadButton"),o=document.getElementById("review-link");o.href=`https://chromewebstore.google.com/detail/${chrome.runtime.id}/reviews`;async function n(){var i;try{r.textContent="Refreshing...",r.disabled=!0;const e=await chrome.tabs.query({});for(const t of e)t.id&&!((i=t.url)!=null&&i.startsWith("chrome://"))&&await chrome.tabs.reload(t.id);setTimeout(()=>{r.textContent="Tabs Refreshed!"},1e3)}catch(e){console.error("Error reloading tabs:",e),r.textContent="Error - Try Again",r.disabled=!1}}r.addEventListener("click",n)});
